#ifndef API_AMPLIFIER_H
#define API_AMPLIFIER_H

#include <QObject>
#include <QSerialPort>
#include <QElapsedTimer>

#include "dev_amplifier.h"
#include "logs.h"

using namespace Amplifier;

class API_Amplifier : public QObject
{
    Q_OBJECT
public:
    explicit API_Amplifier(const QString& name, const QString& port_name, const qint32 baudRate = 115200, QObject* parent = nullptr);

    ~API_Amplifier();

    void getQueryStatus(QueryUnitResponse& status);

    void getOnlineStatus(GetOnlineStatusResponse& status);

    bool isLinked();

public:

    void queryStatus();

    void queryOnlineStatus();

    void controlSetSwitch(quint8 channel_id, quint8 switch_status);

    void controlSetWorkMode(quint8 channel_id, quint8 work_mode);

    void controlSetCurrent(quint8 pump_id, quint16 current);

    void controlSetOutputPower(quint8 channel_id, double output_power);

    void controlSetOnlineStatus(quint8 online_status);

private:
    void getProtocol(QByteArray& frame_head, const quint16 cmdid, const quint16 len);

    void configInfo();

    void startQuery();

private:
    void init();

private:
    QSerialPort* m_serialPort = nullptr;

    QueryUnitResponse m_query_status;

    GetOnlineStatusResponse m_online_status;

    QString m_port_name = "";

    QString m_name = "";

    qint32 m_baud_rate = 115200;

    QElapsedTimer m_elapsed_timer;

    Logs* m_log = nullptr;
};

#endif // API_AMPLIFIER_H
