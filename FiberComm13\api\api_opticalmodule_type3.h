#ifndef API_OPTICALMODULE_TYPE3_H
#define API_OPTICALMODULE_TYPE3_H

#include <QObject>
#include <QString>
#include <QElapsedTimer>
#include <QCache>
#include <QMutex>
#include <QTimer>

#include "qttelnet.h"
#include "logs.h"
#include "dev_opticalmodule_type3.h"

using namespace OpticalModule;

class API_OpticalModule_Type3 : public QObject
{
    Q_OBJECT
public:
    typedef enum TYPE
    {
        READ,
        WRITE,
        READ_MULTI,
        OTHER
    }Type;

    explicit API_OpticalModule_Type3(const QString name, QObject* parent = nullptr);

    ~API_OpticalModule_Type3_Type3();

    void getQueryStatus(CacheValue& value);

    void queryData();

    bool isLinked();

    void controlModuleStatus(const QString & moduleStatus);

    void controlOutputPower(const double output_power);

    void controlCarrierFreq(const quint16 freq_interval, const quint16 channel_num);

    void controlCarrierFreqOffset(const qint16 freq_compensate);

    void controlSentPRBSOpen();

    void controlRecvPRBSOpen();

    void controlPRBSClose();

    void controlModOpen();

    void controlModClose();

    void controlFreqAccurateOpen();

    void controlFreqAccurateClose();

    void controlLaserOn();

    void controlStartScan(qint16 start_bin, qint16 end_bin, qint16 freq_vary);

    void controlLaserOff();

public:
    void handleTelnetData(const QString& message);//解析数据

public:
    void login(const QString& ip, const quint16 port);

    void setUserInfo(const QString& username, const QString& password);

    void reConnect();

signals:
    void signalControlResult(const QString& result);

private:
    void init();

    void handleQueryMessage(const QString& regAddr, const QString& result);

    void handleMessage(const QString& full_message, QString& part_message, QString& regAddr, QString& result, Type& type);

    void delay_msec(qint32 msec);

public:
    void sendData(const QString& data);

private:
    QMutex m_mutex;

    QtTelnet* m_telnet;

    QString m_username = "admin";

    QString m_password = "admin123";

    QString m_ip = "";

    quint16 m_port = 0;

    QString m_name = "";

    bool m_log_status = false;

    CacheValue m_cache_values;

    QElapsedTimer m_elapsed_timer;

    Logs* m_log = nullptr;

    bool m_set_output_power = false;
    double m_value_output_power = 0;
    bool m_set_carrier_freq = false;
    quint16 m_value_carrier_freq = 0;
    bool m_status_sent_open_prbs = false;
    bool m_status_recv_open_prbs = false;
    bool m_status_close_all_prbs = false;
    bool m_mod_open = false;
    bool m_mod_close = false;
    bool m_freq_accurate_open = false;
    bool m_freq_accurate_close = false;

    QTimer* m_timer;

    QVariantMap m_params;

    quint8 flag_sweep  = 1;
    qint8 freq_vary_sign = 0;
};

#endif // API_OPTICALMODULE_TYPE3_H
