#include "api_redisclient.h"
#include <QDebug>

API_RedisClient* API_RedisClient::instance = nullptr;

void API_RedisClient::login(const QString &ip, const quint16 port)
{
    m_ip = ip;
    m_port = port;

    QByteArray byteIP = m_ip.toLatin1();

    m_context = redisConnect(byteIP.data(), m_port);

    if(m_context == nullptr || m_context->err)
    {
        if(m_context)
        {
            qDebug() << "error :" << m_context->errstr;
            redisFree(m_context);
        }
        else {
            qDebug() << "cannot allocate redis context";
        }
    }

    QString strPassword = QString("AUTH %1").arg("123456");

    QByteArray bytePassword = strPassword.toLatin1();

    m_reply = (redisReply*)redisCommand(m_context, bytePassword.data());

    if(m_reply->type == REDIS_REPLY_ERROR)
    {
        qDebug() << "redis log in false";
    }
    else {
        qDebug() << "redis log in success";
    }
}

void API_RedisClient::setValue(const QString &key, const QString &value)
{
    QString str = QString("SET %1 %2").arg(key).arg(value);

    QByteArray byte = str.toLatin1();

    redisCommand(m_context, byte.data());
}

QString API_RedisClient::getValue(const QString &key)
{
    QString str = QString("GET %1").arg(key);

    QByteArray byte = str.toLatin1();

    m_reply = (redisReply*)redisCommand(m_context, byte.data());



    if(m_reply->type == REDIS_REPLY_STRING)
    {
        return m_reply->str;
    }

    return "";
}

API_RedisClient::API_RedisClient()
{

}

API_RedisClient::~API_RedisClient()
{
    freeReplyObject(m_reply);

    redisFree(m_context);
}
