#ifndef DEV_OPTICALMODULE_H
#define DEV_OPTICALMODULE_H

#include <QtGlobal>
#include <QString>
#include <QStringList>
#include <QMap>

namespace OpticalModule {

typedef struct CACHE_VALUE
{
    QString REG_STATUS;
    QString REG_TEMP;
    QString REG_OUTPUT_POWER;
    QString REG_INPUT_POWER;
    QString REG_SNR;
    QString REG_SENT_CARRIER_FREQ_1;
    QString REG_SENT_CARRIER_FREQ_2;
    QString REG_CARRIER_FREQ_OFFSET;
    QString REG_BIT_COUNT_1;
    QString REG_BIT_COUNT_2;
    QString REG_BIT_COUNT_3;
    QString REG_BIT_COUNT_4;
    QString REG_EVM;
    QString REG_ERROR_BIT_COUNT_1;
    QString REG_ERROR_BIT_COUNT_2;
    QString REG_ERROR_BIT_COUNT_3;
    QString REG_ERROR_BIT_COUNT_4;
    QString REG_PRBS_B011;
    QString REG_PRBS_B012;
    QString REG_PRBS_9041;
    QString REG_PRBS_9042;
    QString REG_CONTROL_CARRIER_FREQ;
    QString REG_CONTROL_DOPPLER_VALUE;
    QString REG_CONTROL_OUTPUT_POWER;
    QString REG_ERROR_CODE_RATE_1;
    QString REG_ERROR_CODE_RATE_2;
    QString REG_MOD_STATUS;
    QString REG_BC04;
    QString REG_B050;
}CacheValue;

const static QString& TAIL = "\r\n";

const static QString& GET_STATUS = "cfp2 read b016\r\n";                        //查询光模块状态
const static QString& GET_TEMP = "cfp2 read b02f\r\n";                          //查询光模块温度
const static QString& GET_OUTPUT_POWER = "cfp2 read b4a0\r\n";                  //查询光模块发射端输出功率
const static QString& GET_CONTROL_CARRIER_FREQ = "cfp2 read b400\r\n";          //查询控制的载波频率
const static QString& GET_CONTROL_OUTPUT_POWER = "cfp2 read b410\r\n";          //查询光模块发射端输出功率
const static QString& GET_CONTROL_DOPPLER_VALUE = "cfp2 read b430\r\n";          //查询控制的多普勒预补偿值
const static QString& GET_INPUT_POWER = "cfp2 read b4e0\r\n";                   //查询光模块接收端输入功率
const static QString& GET_SNR = "cfp2 read ba00\r\n";                           //查询光模块接收端光信噪比
const static QString& GET_MOD_STATUS = "cfp2 read bc05\r\n";                    //查询激光基带调制状态
const static QString& GET_SENT_CARRIER_FREQ_1 = "cfp2 read b450\r\n";           //查询光模块发射端配置的载波频率
const static QString& GET_SENT_CARRIER_FREQ_2 = "cfp2 read b460\r\n";           //查询光模块发射端配置的载波频率
const static QString& GET_CARRIER_FREQ_OFFSET = "cfp2 read b9c0\r\n";           //查询光模块接收端的载波频率偏移值
const static QString& GET_EVM = "cfp2 read 9098\r\n";                           //查询光模块接收端的EVM
const static QString& GET_ERROR_CODE_RATE = "cfp2 read 9048 length 2\r\n";    //查询光模块误码率
const static QString& GET_BIT_COUNT_1 = "cfp2 read 9350 length 4\r\n";          //查询光模接收端PRBS的bit计数值
//const static QString& GET_BIT_COUNT_2 = "cfp2 read 9351\r\n";                 //查询光模接收端PRBS的bit计数值
//const static QString& GET_BIT_COUNT_3 = "cfp2 read 9352\r\n";                 //查询光模接收端PRBS的bit计数值
//const static QString& GET_BIT_COUNT_4 = "cfp2 read 9353\r\n";                 //查询光模接收端PRBS的bit计数值
const static QString& GET_ERROR_BIT_COUNT_1 = "cfp2 read 9360 length 4\r\n";    //查询光模接收端PRBS的错误bit计数值
//const static QString& GET_ERROR_BIT_COUNT_2 = "cfp2 read 9361\r\n";           //查询光模接收端PRBS的错误bit计数值
//const static QString& GET_ERROR_BIT_COUNT_3 = "cfp2 read 9362\r\n";           //查询光模接收端PRBS的错误bit计数值
//const static QString& GET_ERROR_BIT_COUNT_4 = "cfp2 read 9363\r\n";           //查询光模接收端PRBS的错误bit计数值
const static QString& GET_PRBS_STATUS = "cfp2 read 9041 length 2\r\n";    //查询光模接收端PRBS的错误bit计数值
const static QString& GET_BC04 = "cfp2 read bc04\r\n";    //查询bc04
const static QString& GET_B050 = "cfp2 read b050\r\n";    //查询b050

const static QString& SET_STATUS = "cfp2 write b010 ";                          //控制光模块的复位、高/低功耗、通道开/闭状态
const static QString& SET_OUTPUT_POWER = "cfp2 write b410 ";                    //控制光模块的理想输出功率
const static QString& SET_CARRIER_FREQ = "cfp2 write b400 ";                    //控制光模块的载波频率值
const static QString& SET_CARRIER_FREQ_PRESET = "cfp2 write b430 ";             //控制光模块CFO频率预补偿

const static quint16 REG_STATUS = 0xb016;                //查询光模块状态
const static quint16 REG_TEMP = 0xb02f;                //查询光模块状态
const static quint16 REG_CONTROL_CARRIER_FREQ = 0xb400;  //查询控制的载波频率
const static quint16 REG_CONTROL_OUTPUT_POWER = 0xb410;  //查询光模块发射端输出功率
const static quint16 REG_CONTROL_DOPPLER_VALUE = 0xb430; //查询控制的多普勒预补偿值
const static quint16 REG_OUTPUT_POWER = 0xb4a0;          //查询光模块发射端输出功率
const static quint16 REG_INPUT_POWER = 0xb4e0;           //查询光模块接收端输入功率
const static quint16 REG_SNR = 0xba00;                   //查询光模块接收端光信噪比
const static quint16 REG_MOD_STATUS = 0xbc05;            //查询激光基带调制状态
const static quint16 REG_SENT_CARRIER_FREQ_1 = 0xb450;   //查询光模块发射端配置的载波频率
const static quint16 REG_SENT_CARRIER_FREQ_2 = 0xb460;   //查询光模块发射端配置的载波频率
const static quint16 REG_CARRIER_FREQ_OFFSET = 0xb9c0;   //查询光模块接收端的载波频率偏移值
const static quint16 REG_EVM = 0x9098;                   //查询光模接收端EVM
const static quint16 REG_BIT_COUNT_1 = 0x9350;           //查询光模接收端PRBS的bit计数值
const static quint16 REG_BIT_COUNT_2 = 0x9351;           //查询光模接收端PRBS的bit计数值
const static quint16 REG_BIT_COUNT_3 = 0x9352;           //查询光模接收端PRBS的bit计数值
const static quint16 REG_BIT_COUNT_4 = 0x9353;           //查询光模接收端PRBS的bit计数值
const static quint16 REG_ERROR_BIT_COUNT_1 = 0x9360;     //查询光模接收端PRBS的错误bit计数值
const static quint16 REG_ERROR_BIT_COUNT_2 = 0x9361;     //查询光模接收端PRBS的错误bit计数值
const static quint16 REG_ERROR_BIT_COUNT_3 = 0x9362;     //查询光模接收端PRBS的错误bit计数值
const static quint16 REG_ERROR_BIT_COUNT_4 = 0x9363;     //查询光模接收端PRBS的错误bit计数值
const static quint16 REG_ERROR_CODE_RATE_1 = 0x9048;                   //查询光模接纠前误码率
const static quint16 REG_ERROR_CODE_RATE_2 = 0x9049;                   //查询光模接纠前误码率
const static quint16 REG_PRBS_B011 = 0xb011;
const static quint16 REG_PRBS_B012 = 0xb012;
const static quint16 REG_PRBS_9041 = 0x9041;
const static quint16 REG_PRBS_9042 = 0x9042;
const static quint16 REG_BC04 = 0xbc04;
const static quint16 REG_B050 = 0xb050;

const static QStringList& QUERY_COMMAND_LIST(QStringList()
                                   << GET_STATUS << GET_OUTPUT_POWER << GET_INPUT_POWER << GET_SNR
                                   << GET_SENT_CARRIER_FREQ_1 << GET_SENT_CARRIER_FREQ_2 << GET_CARRIER_FREQ_OFFSET
                                   << GET_BIT_COUNT_1 /*<< GET_BIT_COUNT_2 << GET_BIT_COUNT_3 << GET_BIT_COUNT_4*/
                                   << GET_ERROR_BIT_COUNT_1 /*<< GET_ERROR_BIT_COUNT_2 << GET_ERROR_BIT_COUNT_3 << GET_ERROR_BIT_COUNT_4*/);

const static QList<quint16> QUERY_REG_ADDR_LIST(QList<quint16>()
                                                << REG_STATUS << REG_OUTPUT_POWER << REG_INPUT_POWER << REG_SNR
                                                << REG_SENT_CARRIER_FREQ_1 << REG_SENT_CARRIER_FREQ_2 << REG_CARRIER_FREQ_OFFSET
                                                << REG_BIT_COUNT_1 << REG_BIT_COUNT_2 << REG_BIT_COUNT_3 << REG_BIT_COUNT_4
                                                << REG_ERROR_BIT_COUNT_1 << REG_ERROR_BIT_COUNT_2 << REG_ERROR_BIT_COUNT_3 << REG_ERROR_BIT_COUNT_4);

static QMap<quint16, QString> QUERY_REG_ADDR_COMMAND_MAP;

static void initQueryMap()
{
    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_STATUS, GET_STATUS);
    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_OUTPUT_POWER, GET_OUTPUT_POWER);
    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_INPUT_POWER, GET_INPUT_POWER);
    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_SNR, GET_SNR);
    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_SENT_CARRIER_FREQ_1, GET_SENT_CARRIER_FREQ_1);
    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_SENT_CARRIER_FREQ_2, GET_SENT_CARRIER_FREQ_2);
    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_CARRIER_FREQ_OFFSET, GET_CARRIER_FREQ_OFFSET);
    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_BIT_COUNT_1, GET_BIT_COUNT_1);
//    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_BIT_COUNT_2, GET_BIT_COUNT_2);
//    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_BIT_COUNT_3, GET_BIT_COUNT_3);
//    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_BIT_COUNT_4, GET_BIT_COUNT_4);
    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_ERROR_BIT_COUNT_1, GET_ERROR_BIT_COUNT_1);
//    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_ERROR_BIT_COUNT_2, GET_ERROR_BIT_COUNT_2);
//    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_ERROR_BIT_COUNT_3, GET_ERROR_BIT_COUNT_3);
//    QUERY_REG_ADDR_COMMAND_MAP.insert(REG_ERROR_BIT_COUNT_4, GET_ERROR_BIT_COUNT_4);
}

}

#endif // DEV_OPTICALMODULE_H
