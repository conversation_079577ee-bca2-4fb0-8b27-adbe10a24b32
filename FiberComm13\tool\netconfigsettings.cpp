#include "netconfigsettings.h"

#include <QSettings>
#include <QDebug>
#include <QCoreApplication>

NetConfigSettings* NetConfigSettings::instance = nullptr;

QVariantMap NetConfigSettings::getNetworkInfo(const QString &devid)
{
    return m_network_info.value(devid);
}

NetConfigSettings::NetConfigSettings()
{
    readConfigInfo();
}

NetConfigSettings::NetConfigSettings(const NetConfigSettings &)
{

}

NetConfigSettings &NetConfigSettings::operator=(const NetConfigSettings &)
{

}

void NetConfigSettings::readConfigInfo()
{

    QSettings settings(QString("comm.ini"), QSettings::IniFormat);

    settings.beginGroup("OpticalModule1");
    QVariantMap optical_module_map_1;
    QString ip = settings.value("ip").toString();
    quint16 port = settings.value("port").toUInt();
    QString username = settings.value("username").toString();
    QString password = settings.value("password").toString();
    optical_module_map_1.insert("ip", ip);
    optical_module_map_1.insert("port", port);
    optical_module_map_1.insert("username", username);
    optical_module_map_1.insert("password", password);
    settings.endGroup();

    settings.beginGroup("OpticalModule2");
    QVariantMap optical_module_map_2;
    QString ip_2 = settings.value("ip").toString();
    quint16 port_2 = settings.value("port").toUInt();
    QString username_2 = settings.value("username").toString();
    QString password_2 = settings.value("password").toString();
    optical_module_map_2.insert("ip", ip_2);
    optical_module_map_2.insert("port", port_2);
    optical_module_map_2.insert("username", username_2);
    optical_module_map_2.insert("password", password_2);
    settings.endGroup();

    settings.beginGroup("Amplifier");
    QVariantMap amplifier_1;
    QString portname = settings.value("portname").toString();
    qint32 baudrate = settings.value("baudrate").toInt();
    amplifier_1.insert("portname", portname);
    amplifier_1.insert("baudrate", baudrate);
    settings.endGroup();

    m_network_info.insert("OpticalModule1", optical_module_map_1);
    m_network_info.insert("OpticalModule2", optical_module_map_2);
    m_network_info.insert("Amplifier", amplifier_1);

    if(ip.isEmpty())
    {
        initConfigInfo();
    }
}

void NetConfigSettings::initConfigInfo()
{
    QSettings settings("comm.ini", QSettings::IniFormat);

    settings.beginGroup("OpticalModule1");
    settings.setValue("ip", "**********");
    settings.setValue("port", 1234);
    settings.setValue("username", "admin");
    settings.setValue("password", "admin123");
    settings.endGroup();

    settings.beginGroup("OpticalModule2");
    settings.setValue("ip", "**********");
    settings.setValue("port", 1234);
    settings.setValue("username", "admin");
    settings.setValue("password", "admin123");
    settings.endGroup();

    settings.beginGroup("Amplifier");
    settings.setValue("portname", "COM4");
    settings.setValue("baudrate", 115200);
    settings.endGroup();
}
