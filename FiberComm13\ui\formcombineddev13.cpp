#include "formcombineddev13.h"
#include "ui_formcombineddev13.h"
#include "dev_amplifier.h"
#include "dev_opticalmodule.h"
#include "netconfigsettings.h"
#include "api_redisclient.h"

#include <QtMath>
#include <QStringLiteral>
#include <QMessageBox>
#include <QCompleter>
#include <QBitArray>
#include <limits.h>
#include <QFile>
#include <QTimer>
#include <QDateTime>
#include <QDesktopServices>
#include <QDesktopWidget>
#include <thread>
#include <QApplication>
#include <QList>
using namespace OpticalModule;

static quint64 s_total_bit_count_2 = 0;
static quint64 s_error_bit_count_2 = 0;

FormCombinedDev13::FormCombinedDev13(MachineType type, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::FormCombinedDev13), m_machine_type(type)
  , m_timer_module_status_1(new QTimer(this)), m_timer_module_status_2(new QTimer(this))
  , m_timer_reset_ready_time_1(new QTimer(this)), m_timer_reset_ready_time_2(new QTimer(this))
{
    ui->setupUi(this);
    QList<QPushButton*> settingBtn = {ui->btnSet_5, ui->btnSet_1,ui->btnSet_2,ui->btnSet_3,ui->btnSet_4,ui->btnSet_6,ui->btnSet_7,ui->btnSet_8,ui->btnSet_10,ui->btnSet_11,ui->btnSet_12,ui->btnSet_13,ui->btnSet_14,ui->btnSet_15,ui->btnSet_16,ui->btnSet_17,ui->btnSet_18,ui->btnSet_19,ui->btnSet_20, ui->btnSet_21, ui->btnSet_22, ui->btnSet_25, ui->btnSet_26, ui->btnSet_28, ui->btnSet_23, ui->btnSet_9, ui->btnSet_27};
    QList<QPushButton*> sendBtn = {ui->btnAmplifier,ui->btnOpenData,ui->btnRecordData};
    QList<QLineEdit*> LineEdit = {ui->ctrl_start_freq, ui->ctrl_end_freq, ui->ctrl_scan_rate, ui->ctrl_output_power_1,ui->ctrl_hpa_output_power_1,ui->ctrl_current_1,ui->ctrl_hpa_output_power_2,ui->sta_current_1,ui->wave_length_1,ui->wave_length_2,ui->sta_hpa_workmode_1,ui->sta_hpa_workmode_2,ui->ctrl_current_2,ui->sta_current_2, ui->ctrl_freq_1, ui->ctrl_freq_2, ui->ctrl_wave_compensate_1, ui->ctrl_wave_compensate_2, ui->ctrl_start_bin, ui->ctrl_freq_compensate_1, ui->ctrl_freq_compensate_2};
    QList<QGroupBox*> GroupBox = findChildren<QGroupBox*>();
    QList<QLabel*> showLabel = {ui->label_2,ui->label_3,ui->label_4,ui->label_5,ui->label_6,ui->label_7,ui->label_8,ui->label_9,ui->label_10,ui->label_11,ui->label_12,ui->label_13,ui->label_14,ui->label_15,ui->label_16,ui->label_17,ui->label_18,ui->label_19,ui->label_20,ui->label_21,ui->label_22,ui->label_24,ui->label_25,ui->label_26,ui->label_27,ui->label_28,ui->label_29,ui->label_31,ui->label_32,ui->label_33,ui->label_34,ui->label_35,ui->label_36,ui->label_37,ui->label_38,ui->label_39,ui->label_40, ui->label_41, ui->label_42,ui->label_43, ui->label_46, ui->label_47, ui->label_23, ui->label_30, ui->label_44, ui->label_48, ui->label_45};
    QList<QLabel*> textLabel = {ui->module_temp_1,ui->module_temp_2,ui->output_power_1,ui->output_power_2,ui->hpa_input_power_1,ui->hpa_input_power_2,ui->hpa_output_power_1,ui->hpa_output_power_2,ui->carrier_power_1,ui->carrier_power_2,ui->freq_offset_2,ui->snr_2,ui->evm_2,ui->error_code_rate_2,ui->bit_count_2, ui->wave_len_1, ui->wave_len_2};
    QList<QLabel*> LampPNG = {ui->sta_ready_1,ui->sta_ready_2,ui->sta_reset_1,ui->sta_reset_2,ui->sta_trans_1,ui->sta_trans_2,ui->sta_amplifier_status_1,ui->sta_amplifier_status_2};

    for(int i = 0;i < settingBtn.size();i++){
        settingBtn[i]->setProperty("settingBtn","setBtn") ;
    };
    for(int j = 0;j<sendBtn.size();j++){
        sendBtn[j]->setProperty("sendBtn","openData") ;
    };
    for(int k = 0;k<LineEdit.size();k++){
        LineEdit[k]->setProperty("LineEdit","edit") ;
    };
    for(int l = 0;l<GroupBox.size();l++){
        GroupBox[l]->setProperty("GroupBox","zml") ;
    };
    for(int o = 0;o<showLabel.size();o++){
        showLabel[o]->setProperty("showLabel","attrbiute") ;
    };
    for(int p = 0;p<textLabel.size();p++){
        textLabel[p]->setProperty("textLabel","params") ;
    };
    for(int m = 0;m<LampPNG.size();m++){
        LampPNG[m]->setProperty("lampLabel","png") ;
    };
    setWindowTitle(QStringLiteral("100G光模块上位机"));

    API_RedisClient::getInstance()->login("127.0.0.1", 6379);

    initView();

    initMember();

    initDefaultParams();

    loadLastParams();

    QDesktopWidget* desktop = QApplication::desktop();

    move(200, 10);
    ui->comboBox_light->addItem("299792458");
    ui->comboBox_light->addItem("300000000");
    QString filepath="my.txt";
    QFile light_speedfile(filepath);
    if(!light_speedfile.exists()){
        if(!light_speedfile.open(QIODevice::WriteOnly|QIODevice::Text)){

        }
        light_speedfile.close();
    }
    if(!light_speedfile.open(QIODevice::ReadOnly|QIODevice::Text)){
        qWarning()<<"file open filed";
    }
    QTextStream in(&light_speedfile);
    QString content=in.readAll();
    light_speedfile.close();
    ui->comboBox_light->setCurrentIndex(content.toInt());

}

FormCombinedDev13::~FormCombinedDev()
{
    delete ui;
}

void FormCombinedDev13::closeEvent(QCloseEvent */*event*/)
{
    if(m_form_amplifier != nullptr)
    {
        m_form_amplifier->deleteLater();
        m_form_amplifier = nullptr;
    }
}

void FormCombinedDev13::refData()
{
    CacheValue queryStatus1, queryStatus2;

    if(m_api_optical_module_1->isLinked()){

        m_api_optical_module_1->getQueryStatus(queryStatus1);

        quint16 moduleStatus = queryStatus1.REG_STATUS.toUShort(nullptr, 16);


        switch (moduleStatus) {
        case 0xffff://复位
            setLightColor(ui->sta_reset_1, GREEN);      //复位
            setLightColor(ui->sta_ready_1, GRAY);       //就绪
            setLightColor(ui->sta_trans_1, GRAY);       //传输
            break;
        case 0x0008://通道关闭
            setLightColor(ui->sta_reset_1, GRAY);
            setLightColor(ui->sta_ready_1, GREEN);
            setLightColor(ui->sta_trans_1, GRAY);
            break;
        case 0x0020://通道打开
            setLightColor(ui->sta_reset_1, GRAY);
            setLightColor(ui->sta_ready_1, GREEN);
            setLightColor(ui->sta_trans_1, GREEN);
            break;
        default:
            setLightColor(ui->sta_reset_1, YELLOW);
            setLightColor(ui->sta_ready_1, YELLOW);
            setLightColor(ui->sta_trans_1, YELLOW);
            break;
        }

        qint16 module_temp_1 = queryStatus1.REG_TEMP.toUShort(nullptr, 16);

        ui->module_temp_1->setText(QString::number(module_temp_1 / 256.0, 'f', 1));

        qint16 outputPower = queryStatus1.REG_OUTPUT_POWER.toUShort(nullptr, 16);

        ui->output_power_1->setText(QString::number(outputPower * 0.01, 'f', 2));

        quint16 carrier_freq_1 = queryStatus1.REG_SENT_CARRIER_FREQ_1.toUShort(nullptr, 16);

        quint16 carrier_freq_2 = queryStatus1.REG_SENT_CARRIER_FREQ_2.toUShort(nullptr, 16);

        double dCarrierPower = carrier_freq_2 * 0.05 + carrier_freq_1 * 1000;

        ui->carrier_power_1->setText(QString::number(dCarrierPower, 'f', 2));

        quint16 open_prbs_status = queryStatus1.REG_PRBS_9041.toUShort(nullptr, 16);

        switch (open_prbs_status) {
        case 0xf:
            setLightColor(ui->sta_open_prbs, GREEN);
            break;
        default:
            setLightColor(ui->sta_open_prbs, GRAY);
            break;
        }

        quint16 mod_status = queryStatus1.REG_MOD_STATUS.toUShort(nullptr, 16);

        switch (mod_status) {
        case 0xf:
            setLightColor(ui->sta_mod_status_1, GRAY);
            break;
        default:
            setLightColor(ui->sta_mod_status_1, GREEN);
            break;
        }

        double freq_set = ui->ctrl_freq_compensate_1->text().toDouble();

        ui->wave_len_1->setText(QString::number(freqSet2waveLen(m_api_optical_module_1, freq_set), 'f', 3));


        quint16 bc04_status = queryStatus1.REG_BC04.toUShort(nullptr, 16);
        quint16 b050_status = queryStatus1.REG_B050.toUShort(nullptr, 16);
        quint16 bc05_status = queryStatus1.REG_MOD_STATUS.toUShort(nullptr, 16);

        if((bc05_status == 0x070b) && (b050_status==0x8040))
        {
            setLightColor(ui->sta_raser_on, GREEN);
        }
        else {
            setLightColor(ui->sta_raser_on, GRAY);
        }

        if((bc04_status == 0x0708) && (b050_status==0x8040))
        {
            setLightColor(ui->sta_scanstart, GREEN);
        }
        else {
            setLightColor(ui->sta_scanstart, GRAY);
        }

        if((bc05_status == 0x070a) && (b050_status==0x8040))
        {
            setLightColor(ui->sta_raser_off, GREEN);
        }
        else {
            setLightColor(ui->sta_raser_off, GRAY);
        }
    }
    else {
        m_api_optical_module_1->reConnect();

        setLightColor(ui->sta_reset_1, GRAY);      //复位
        setLightColor(ui->sta_ready_1, GRAY);     //传输通道关闭
        setLightColor(ui->sta_trans_1, GRAY);      //低功�?
        ui->module_temp_1->setText(OFFLINE_VALUE);
        ui->output_power_1->setText(OFFLINE_VALUE);
        ui->carrier_power_1->setText(OFFLINE_VALUE);
        setLightColor(ui->sta_open_prbs, GRAY);
        setLightColor(ui->sta_mod_status_1, GRAY);
        ui->wave_len_1->setText(OFFLINE_VALUE);
        setLightColor(ui->sta_raser_on, GRAY);
        setLightColor(ui->sta_scanstart, GRAY);
        setLightColor(ui->sta_raser_off, GRAY);
    }

    if(m_api_optical_module_2->isLinked()){

        m_api_optical_module_2->getQueryStatus(queryStatus2);

        quint16 moduleStatus = queryStatus2.REG_STATUS.toUShort(nullptr, 16);

        switch (moduleStatus) {
        case 0xffff://复位
            setLightColor(ui->sta_reset_2, GREEN);      //复位
            setLightColor(ui->sta_ready_2, GRAY);       //就绪
            setLightColor(ui->sta_trans_2, GRAY);       //传输
            break;
        case 0x0008://通道关闭
            setLightColor(ui->sta_reset_2, GRAY);
            setLightColor(ui->sta_ready_2, GREEN);
            setLightColor(ui->sta_trans_2, GRAY);
            break;
        case 0x0020://通道打开
            setLightColor(ui->sta_reset_2, GRAY);
            setLightColor(ui->sta_ready_2, GREEN);
            setLightColor(ui->sta_trans_2, GREEN);
            break;
        default:
            setLightColor(ui->sta_reset_2, YELLOW);
            setLightColor(ui->sta_ready_2, YELLOW);
            setLightColor(ui->sta_trans_2, YELLOW);
            break;
        }

        qint16 module_temp_2 = queryStatus2.REG_TEMP.toUShort(nullptr, 16);

        ui->module_temp_2->setText(QString::number(module_temp_2 / 256.0, 'f', 1));

        qint16 outputPower = queryStatus2.REG_INPUT_POWER.toUShort(nullptr, 16);

        ui->output_power_2->setText(QString::number(outputPower * 0.01, 'f', 2));

        quint16 carrier_freq_1 = queryStatus2.REG_SENT_CARRIER_FREQ_1.toUShort(nullptr, 16);

        quint16 carrier_freq_2 = queryStatus2.REG_SENT_CARRIER_FREQ_2.toUShort(nullptr, 16);

        double dCarrierPower = carrier_freq_2 * 0.05 + carrier_freq_1 * 1000;

        ui->carrier_power_2->setText(QString::number(dCarrierPower, 'f', 2));

        qint16 sta_carrie_offset = queryStatus2.REG_CARRIER_FREQ_OFFSET.toUShort(nullptr, 16);

        ui->freq_offset_2->setText(QString::number(sta_carrie_offset));

        qint16 sta_evm = queryStatus2.REG_EVM.toUShort(nullptr, 16);

        ui->evm_2->setText(QString::number(sta_evm));

        quint16 sta_snr = queryStatus2.REG_SNR.toUShort(nullptr, 16);

        ui->snr_2->setText(QString::number(sta_snr * 0.1, 'f', 1));

        QString str_bit_count_1 = queryStatus2.REG_BIT_COUNT_1.remove("0x");
        QString str_bit_count_2 = queryStatus2.REG_BIT_COUNT_2.remove("0x");
        QString str_bit_count_3 = queryStatus2.REG_BIT_COUNT_3.remove("0x");
        QString str_bit_count_4 = queryStatus2.REG_BIT_COUNT_4.remove("0x");


        QString str_bit_count = QString("0x%1%2%3%4")
                .arg(str_bit_count_1).arg(str_bit_count_2)
                .arg(str_bit_count_3).arg(str_bit_count_4);

        quint64 sta_bit_count = str_bit_count.toULongLong(nullptr, 16);

        QString str_error_bit_count_1 = queryStatus2.REG_ERROR_BIT_COUNT_1.remove("0x");
        QString str_error_bit_count_2 = queryStatus2.REG_ERROR_BIT_COUNT_2.remove("0x");
        QString str_error_bit_count_3 = queryStatus2.REG_ERROR_BIT_COUNT_3.remove("0x");
        QString str_error_bit_count_4 = queryStatus2.REG_ERROR_BIT_COUNT_4.remove("0x");

        QString str_error_bit_count = QString("0x%1%2%3%4")
                .arg(str_error_bit_count_1).arg(str_error_bit_count_2)
                .arg(str_error_bit_count_3).arg(str_error_bit_count_4);

        quint64 sta_error_bit_count = str_error_bit_count.toULongLong(nullptr, 16);

        ui->bit_count_2->setText(QString::number(1.0 * sta_error_bit_count / sta_bit_count, 'e', 7));


        s_total_bit_count_2 = sta_bit_count;
        s_error_bit_count_2 = sta_error_bit_count;

        QByteArray byte_error_code_rate;

        byte_error_code_rate.resize(4);

        QString str_error_code_rate = QString("%1%2")
                .arg(queryStatus2.REG_ERROR_CODE_RATE_1.remove("0x"))
                .arg(queryStatus2.REG_ERROR_CODE_RATE_2.remove("0x"));

        //        qDebug() << queryStatus2.REG_ERROR_CODE_RATE_1 << queryStatus2.REG_ERROR_CODE_RATE_2;

        QByteArray tmp_error_code_rate = str_error_code_rate.toLatin1();

        byte_error_code_rate = QByteArray::fromHex(tmp_error_code_rate.data());

        std::reverse(byte_error_code_rate.begin(), byte_error_code_rate.end());

        float f_error_code_rate = 0;

        ::memcpy(&f_error_code_rate, byte_error_code_rate.data(), sizeof (float));

        ui->error_code_rate_2->setText(QString::number(f_error_code_rate));

        quint16 recv_prbs_status = queryStatus2.REG_PRBS_9042.toUShort(nullptr, 16);

        switch (recv_prbs_status) {
        case 0xf:
            setLightColor(ui->sta_recv_prbs, GREEN);
            break;
        default:
            setLightColor(ui->sta_recv_prbs, GRAY);
            break;
        }




        double freq_set = ui->ctrl_freq_compensate_2->text().toDouble();

        ui->wave_len_2->setText(QString::number(freqSet2waveLen(m_api_optical_module_2, freq_set), 'f', 3));

    }
    else {
        m_api_optical_module_2->reConnect();

        setLightColor(ui->sta_reset_2, GRAY);
        setLightColor(ui->sta_ready_2, GRAY);
        setLightColor(ui->sta_trans_2, GRAY);
        ui->module_temp_2->setText(OFFLINE_VALUE);
        ui->output_power_2->setText(OFFLINE_VALUE);
        ui->carrier_power_2->setText(OFFLINE_VALUE);
        ui->freq_offset_2->setText(OFFLINE_VALUE);
        ui->bit_count_2->setText(OFFLINE_VALUE);
        ui->snr_2->setText(OFFLINE_VALUE);
        ui->evm_2->setText(OFFLINE_VALUE);
        setLightColor(ui->sta_recv_prbs, GRAY);
        ui->wave_len_2->setText(OFFLINE_VALUE);
    }

    QueryUnitResponse queryStatus;

    if(m_api_amplifier->isLinked()){

        m_api_amplifier->getQueryStatus(queryStatus);

        qint16 low_noise_input_freq = queryStatus.low_noise_input_freq;
        qint16 low_noise_output_freq = queryStatus.low_noise_output_freq;

        qint16 high_power_input_freq = queryStatus.high_power_input_freq;
        qint16 high_power_output_freq = queryStatus.high_power_output_freq;

        ui->hpa_input_power_1->setText(QString::number(high_power_input_freq * 0.01, 'f', 2));
        ui->hpa_output_power_1->setText(QString::number(high_power_output_freq * 0.01, 'f', 2));
        ui->hpa_input_power_2->setText(QString::number(low_noise_input_freq * 0.01, 'f', 2));
        ui->hpa_output_power_2->setText(QString::number(low_noise_output_freq * 0.01, 'f', 2));

        quint8 module_status = queryStatus.module_status;
        QByteArray byte_module_status;
        byte_module_status.append(module_status);

        QBitArray bit_module_status = QBitArray::fromBits(byte_module_status, 8);

        if(bit_module_status.at(4))
        {
            setLightColor(ui->sta_amplifier_status_1, GREEN);
        }
        else {
            setLightColor(ui->sta_amplifier_status_1, GRAY);
        }

        if(bit_module_status.at(5))
        {
            setLightColor(ui->sta_amplifier_status_2, GREEN);
        }
        else {
            setLightColor(ui->sta_amplifier_status_2, GRAY);
        }

        if(bit_module_status.at(6))
        {
            ui->sta_hpa_workmode_1->setText("当前APC工作");
        }
        else {
            ui->sta_hpa_workmode_1->setText("当前ACC工作");
        }

        if(bit_module_status.at(7))
        {
            ui->sta_hpa_workmode_2->setText("当前APC工作");
        }
        else {
            ui->sta_hpa_workmode_2->setText("当前ACC工作");
        }

        ui->sta_current_1->setText(QString::number(queryStatus.pump_current_2));
        ui->sta_current_2->setText(QString::number(queryStatus.pump_current_1));
    }
    else {

        ui->hpa_output_power_1->setText(OFFLINE_VALUE);
        ui->hpa_output_power_2->setText(OFFLINE_VALUE);
        setLightColor(ui->sta_amplifier_status_1, GRAY);
        setLightColor(ui->sta_amplifier_status_2, GRAY);
        ui->sta_hpa_workmode_1->setText(OFFLINE_VALUE);
        ui->sta_hpa_workmode_2->setText(OFFLINE_VALUE);
        ui->sta_current_1->setText(OFFLINE_VALUE);
        ui->sta_current_2->setText(OFFLINE_VALUE);
    }


    if(m_record_status)
    {
        if(m_record_file != nullptr)
        {
            QString strBody = QString("%1,%2,%3,%4,%5,%6,%7,%8,%9,%10,%11,%12,%13,%14,%15,%16,%17,%18,%19,%20,%21,%22,%23,%24\n").arg(QTime::currentTime().toString("hh:mm:ss.zzz"))
                    .arg(queryStatus1.REG_STATUS).arg(queryStatus1.REG_TEMP).arg(queryStatus1.REG_OUTPUT_POWER)
                    .arg(queryStatus.high_power_input_freq).arg(queryStatus.high_power_output_freq)
                    .arg(queryStatus1.REG_CONTROL_DOPPLER_VALUE).arg(queryStatus1.REG_SENT_CARRIER_FREQ_1)
                    .arg(queryStatus2.REG_STATUS).arg(queryStatus2.REG_TEMP).arg(queryStatus2.REG_INPUT_POWER)
                    .arg(queryStatus.low_noise_input_freq).arg(queryStatus.low_noise_output_freq)
                    .arg(queryStatus2.REG_CONTROL_DOPPLER_VALUE).arg(queryStatus2.REG_SENT_CARRIER_FREQ_1)
                    .arg(queryStatus2.REG_SNR).arg(queryStatus2.REG_EVM).arg(queryStatus2.REG_ERROR_CODE_RATE_1).arg(queryStatus2.REG_ERROR_CODE_RATE_2)
                    .arg(queryStatus2.REG_BIT_COUNT_1).arg(queryStatus2.REG_BIT_COUNT_2).arg(queryStatus2.REG_BIT_COUNT_3).arg(queryStatus2.REG_BIT_COUNT_4)
                    .arg(queryStatus2.REG_CARRIER_FREQ_OFFSET);

            m_record_file->write(strBody.toLatin1());

            m_record_file->flush();
        }
    }
}

void FormCombinedDev13::on_btnSet_1_clicked()
{
    CacheValue queryStatus;

    m_api_optical_module_1->getQueryStatus(queryStatus);

    if(0xffff == queryStatus.REG_STATUS.toUShort(nullptr, 16))
    {
        m_api_optical_module_1->controlModuleStatus("0");

        m_timer_module_status_1->start();
    }
    else {

        QString ctrl_value = ui->ctrl_module_status_1->itemData(ui->ctrl_module_status_1->currentIndex(), Qt::UserRole).toString();

        m_api_optical_module_1->controlModuleStatus(ctrl_value);
    }


    if(ui->ctrl_module_status_1->currentIndex() == 3)
    {
        m_start_time_1 = QDateTime::currentDateTime();

        m_timer_reset_ready_time_1->start();
    }

    if(ui->ctrl_module_status_1->currentIndex() == 0)
    {
        ui->ctrl_output_power_1->setText("0");
        ui->ctrl_channel_num_1->setCurrentIndex(ui->ctrl_channel_num_1->findText("C18"));

        API_RedisClient::getInstance()->setValue("REG_CONTROL_OUTPUT_POWER_1", "0");
        API_RedisClient::getInstance()->setValue("REG_CONTROL_CARRIER_FREQ_1", QString::number(ui->ctrl_channel_num_1->findText("C18")));
        API_RedisClient::getInstance()->setValue("REG_CONTROL_DOPPLER_VALUE_1", "0");
    }

    API_RedisClient::getInstance()->setValue("REG_STATUS_1", QString::number(ui->ctrl_module_status_1->currentIndex()));
}

void FormCombinedDev13::on_btnSet_2_clicked()
{
    bool ok = false;

    double output_power = ui->ctrl_output_power_1->text().toDouble(&ok);

    if(!ok)
    {
        showMessageBox(QStringLiteral("提示"), QStringLiteral("请输入合法参�?"));

        return;
    }

    if((output_power > ui->ctrl_output_power_1->property(MAX_VALUE).toDouble()) ||
            (output_power < ui->ctrl_output_power_1->property(MIN_VALUE).toDouble()))
    {
        QString tooltip = ui->ctrl_output_power_1->toolTip();

        showMessageBox(QStringLiteral("提示"), ui->ctrl_output_power_1->toolTip());

        return;
    }

    m_api_optical_module_1->controlOutputPower(output_power);

    API_RedisClient::getInstance()->setValue("REG_CONTROL_OUTPUT_POWER_1", ui->ctrl_output_power_1->text());
}

void FormCombinedDev13::on_btnSet_3_clicked()
{
    quint8 channel_id = 1;

    bool ok = false;

    double power = ui->ctrl_hpa_output_power_1->text().toDouble(&ok);

    if(!ok)
    {
        showMessageBox(QStringLiteral("提示"), QStringLiteral("请输入合法参�?"));

        return;
    }

    if((power > ui->ctrl_hpa_output_power_1->property(MAX_VALUE).toDouble()) ||
            (power < ui->ctrl_hpa_output_power_1->property(MIN_VALUE).toDouble()))
    {
        showMessageBox(QStringLiteral("提示"), ui->ctrl_hpa_output_power_1->toolTip());

        return;
    }

    m_api_amplifier->controlSetSwitch(1, 1);

    QTimer::singleShot(2000, this, [=](){
        m_api_amplifier->controlSetOutputPower(channel_id, power);
    });

    API_RedisClient::getInstance()->setValue("HPA_OUTPUT_POWER", ui->ctrl_hpa_output_power_1->text());
}

void FormCombinedDev13::on_btnSet_4_clicked()
{
    m_api_optical_module_1->controlModuleStatus("0x2000");

    QTimer::singleShot(3000, this, [=](){

        quint16 freq_interval = 0x2;

        quint16 channel_num = ui->ctrl_channel_num_1->itemData(ui->ctrl_channel_num_1->currentIndex()).toUInt();

        m_api_optical_module_1->controlCarrierFreq(freq_interval, channel_num);
    });

    QTimer::singleShot(10000, this, [=](){
        m_api_optical_module_1->controlModuleStatus("0x0000");
    });

    API_RedisClient::getInstance()->setValue("REG_CONTROL_CARRIER_FREQ_1", QString::number(ui->ctrl_channel_num_1->currentIndex()));

    m_api_optical_module_1->sendData("cfp2 write b430 0\r\n");
}

void FormCombinedDev13::on_btnSet_6_clicked()
{
    CacheValue queryStatus;

    m_api_optical_module_2->getQueryStatus(queryStatus);

    if(0xffff == queryStatus.REG_STATUS.toUShort(nullptr, 16))
    {
        m_api_optical_module_2->controlModuleStatus("0");

        m_timer_module_status_2->start();
    }
    else {

        QString ctrl_value = ui->ctrl_module_status_2->itemData(ui->ctrl_module_status_2->currentIndex(), Qt::UserRole).toString();

        m_api_optical_module_2->controlModuleStatus(ctrl_value);
    }

    if(ui->ctrl_module_status_2->currentIndex() == 3)
    {
        m_start_time_2 = QDateTime::currentDateTime();

        m_timer_reset_ready_time_2->start();
    }

    if(ui->ctrl_module_status_2->currentIndex() == 0)
    {
        ui->ctrl_channel_num_2->setCurrentIndex(ui->ctrl_channel_num_2->findText("C18"));

        API_RedisClient::getInstance()->setValue("REG_CONTROL_CARRIER_FREQ_2", QString::number(ui->ctrl_channel_num_2->findText("C18")));
        API_RedisClient::getInstance()->setValue("REG_CONTROL_DOPPLER_VALUE_2", "0");
    }

    API_RedisClient::getInstance()->setValue("REG_STATUS_2", QString::number(ui->ctrl_module_status_2->currentIndex()));
}

void FormCombinedDev13::on_btnSet_7_clicked()
{
    quint8 channel_id = 0;

    bool ok = false;

    double power = ui->ctrl_hpa_output_power_2->text().toDouble(&ok);

    if(!ok)
    {
        showMessageBox(QStringLiteral("提示"), QStringLiteral("请输入合法参�?"));

        return;
    }

    if((power > ui->ctrl_hpa_output_power_2->property(MAX_VALUE).toDouble()) ||
            (power < ui->ctrl_hpa_output_power_2->property(MIN_VALUE).toDouble()))
    {
        showMessageBox(QStringLiteral("提示"), ui->ctrl_hpa_output_power_2->toolTip());

        return;
    }

    m_api_amplifier->controlSetSwitch(0, 1);

    QTimer::singleShot(2000, this, [=](){
        m_api_amplifier->controlSetOutputPower(channel_id, power);
    });

    API_RedisClient::getInstance()->setValue("LNA_OUTPUT_POWER", ui->ctrl_hpa_output_power_2->text());
}

void FormCombinedDev13::on_btnSet_8_clicked()
{
    m_api_optical_module_2->controlModuleStatus("0x2000");

    QTimer::singleShot(3000, this, [=](){
        quint16 freq_interval = 0x2;

        quint16 channel_num = ui->ctrl_channel_num_2->itemData(ui->ctrl_channel_num_2->currentIndex()).toUInt();

        m_api_optical_module_2->controlCarrierFreq(freq_interval, channel_num);
    });

    QTimer::singleShot(10000, this, [=](){
        m_api_optical_module_2->controlModuleStatus("0x0000");
    });

    API_RedisClient::getInstance()->setValue("REG_CONTROL_CARRIER_FREQ_2", QString::number(ui->ctrl_channel_num_2->currentIndex()));


    m_api_optical_module_2->sendData("cfp2 write b430 0\r\n");
}

void FormCombinedDev13::on_btnSet_10_clicked()
{
    m_api_optical_module_1->controlSentPRBSOpen();

    ui->btnSet_12->setEnabled(false);
    ui->btnSet_14->setEnabled(false);

    QTimer::singleShot(2000, this, [=](){
        ui->btnSet_12->setEnabled(true);
        ui->btnSet_14->setEnabled(true);
    });
}

void FormCombinedDev13::on_btnSet_11_clicked()
{
    m_api_optical_module_1->controlModOpen();
}

void FormCombinedDev13::on_btnSet_12_clicked()
{
    m_api_optical_module_1->controlPRBSClose();
}

void FormCombinedDev13::on_btnSet_14_clicked()
{
    m_api_optical_module_2->controlRecvPRBSOpen();
}

void FormCombinedDev13::on_btnSet_15_clicked()
{
    m_api_optical_module_2->controlPRBSClose();
}

void FormCombinedDev13::on_btnAmplifier_clicked()
{
    m_form_amplifier->show();
}

void FormCombinedDev13::initView()
{
    resize(1200, 800);

    initControls();

    initComboxs();

    initTooltips();

    ui->btnAmplifier->setHidden(true);
    ui->groupBox_7->setHidden(true);
    ui->groupBox_11->setHidden(true);
}

void FormCombinedDev13::initMember()
{
    QVariantMap network_info_1 = NetConfigSettings::getInstance()->getNetworkInfo("OpticalModule1");
    QString ip_1 = network_info_1["ip"].toString();
    quint16 port_1 = network_info_1["port"].toUInt();
    m_api_optical_module_1 = new API_OpticalModule_Type1("OpticalModule1", this);  // 1型发送端
    m_api_optical_module_1->login(ip_1, port_1);

    QString str_1, str_2;
    std::thread thread_1(&API_OpticalModule_Type1::handleTelnetData, m_api_optical_module_1, str_1);
    thread_1.join();

    QVariantMap network_info_2 = NetConfigSettings::getInstance()->getNetworkInfo("OpticalModule2");
    QString ip_2 = network_info_2["ip"].toString();
    quint16 port_2 = network_info_2["port"].toUInt();
    m_api_optical_module_2 = new API_OpticalModule_Type3("OpticalModule2", this);  // 3型接收端
    m_api_optical_module_2->login(ip_2, port_2);

    std::thread thread_2(&API_OpticalModule_Type3::handleTelnetData, m_api_optical_module_2, str_2);
    thread_2.join();

    QVariantMap network_info_3 = NetConfigSettings::getInstance()->getNetworkInfo("Amplifier");
    QString portname = network_info_3.value("portname").toString();
    qint32 baudrate = network_info_3.value("baudrate").toInt();
    m_api_amplifier = new API_Amplifier("Amplifier", portname, baudrate, this);
    m_form_amplifier = new FormAmplifier("Amplifier", m_api_amplifier);
    m_form_amplifier->setWindowTitle(QStringLiteral("功率放大�?));

    QTimer* timer = new QTimer(this);

    timer->setInterval(500);

    connect(timer, &QTimer::timeout, this, &FormCombinedDev13::refData);

    timer->start();

    for(int i = 0; i < 769; i++)
    {
        table_list.push_back(i * 6.25 + 191300);
    }
}

void FormCombinedDev13::initControls()
{
    QList<QPushButton*> buttonList = findChildren<QPushButton*>();

    foreach(QPushButton* button, buttonList)
    {
        if((button->objectName() == "btnSet_10") || (button->objectName() == "btnSet_12")
                || (button->objectName() == "btnSet_14") || (button->objectName() == "btnSet_15"))
        {
            button->setFixedHeight(FIXED_HEIGHT);
        }
        else {
            button->setFixedSize(FIXED_WIDTH, FIXED_HEIGHT);
        }

        connect(button, &QPushButton::clicked, this, [=](){
            button->setEnabled(false);

            QTimer::singleShot(1000, this, [=](){
                button->setEnabled(true);
            });
        });
    }

    QList<QLabel*> labelList = findChildren<QLabel*>();

    foreach(QLabel* label, labelList)
    {
        if(label->objectName().startsWith("sta_"))
        {
            label->setFixedSize(50, 50);
        }
        else {
            label->setFixedHeight(FIXED_HEIGHT);
        }
    }

    QList<QLineEdit*> lineeditList = findChildren<QLineEdit*>();

    foreach(QLineEdit* lineEdit, lineeditList)
    {
        lineEdit->setFixedHeight(FIXED_HEIGHT);
        lineEdit->setFixedWidth(FIXED_WIDTH);
    }

    QList<QComboBox*> comboxList = findChildren<QComboBox*>();

    foreach(QComboBox* combox, comboxList)
    {
        combox->setFixedHeight(FIXED_HEIGHT);
        combox->setFixedWidth(FIXED_WIDTH);
    }

    m_timer_module_status_1->setInterval(1000);

    connect(m_timer_module_status_1, &QTimer::timeout, this, [=](){

        CacheValue queryStatus;

        m_api_optical_module_1->getQueryStatus(queryStatus);

        if(0xffff != queryStatus.REG_STATUS.toUShort(nullptr, 16))
        {
            QString ctrl_value = ui->ctrl_module_status_1->itemData(ui->ctrl_module_status_1->currentIndex(), Qt::UserRole).toString();

            m_api_optical_module_1->controlModuleStatus(ctrl_value);

            m_timer_module_status_1->stop();
        }
    });

    m_timer_module_status_2->setInterval(1000);

    connect(m_timer_module_status_2, &QTimer::timeout, this, [=](){

        CacheValue queryStatus;

        m_api_optical_module_1->getQueryStatus(queryStatus);

        if(0xffff !=  queryStatus.REG_STATUS.toUShort(nullptr, 16))
        {
            QString ctrl_value = ui->ctrl_module_status_2->itemData(ui->ctrl_module_status_2->currentIndex(), Qt::UserRole).toString();

            m_api_optical_module_2->controlModuleStatus(ctrl_value);

            m_timer_module_status_2->stop();
        }
    });

    m_timer_reset_ready_time_1->setInterval(100);

    connect(m_timer_reset_ready_time_1, &QTimer::timeout, this, [=](){

        CacheValue queryStatus;

        m_api_optical_module_1->getQueryStatus(queryStatus);

        if(queryStatus.REG_STATUS == "0x0020")
        {
            m_timer_reset_ready_time_1->stop();

            showMessageBox("提示", QString("配置时间�?1").arg(m_start_time_1.secsTo(QDateTime::currentDateTime())));
        }

    });

    m_timer_reset_ready_time_2->setInterval(100);

    connect(m_timer_reset_ready_time_2, &QTimer::timeout, this, [=](){

        CacheValue queryStatus;

        m_api_optical_module_2->getQueryStatus(queryStatus);

        if(queryStatus.REG_STATUS == "0x0020")
        {
            m_timer_reset_ready_time_2->stop();

            showMessageBox("提示", QString("配置时间�?1").arg(m_start_time_2.secsTo(QDateTime::currentDateTime())));
        }

    });
}

void FormCombinedDev13::initComboxs()
{
    ui->ctrl_module_status_1->addItem(QStringLiteral("复位"), "0x8000");
    ui->ctrl_module_status_1->addItem(QStringLiteral("通道关闭"), "0x2000");
    ui->ctrl_module_status_1->addItem(QStringLiteral("通道打开"), "0x0000");

    ui->ctrl_module_status_2->addItem(QStringLiteral("复位"), "0x8000");
    ui->ctrl_module_status_2->addItem(QStringLiteral("通道关闭"), "0x2000");
    ui->ctrl_module_status_2->addItem(QStringLiteral("通道打开"), "0x0000");

    ui->ctrl_hpa_workmode_1->addItem(QStringLiteral("ACC模式"), 0);
    ui->ctrl_hpa_workmode_1->addItem(QStringLiteral("APC模式"), 1);

    ui->ctrl_hpa_workmode_2->addItem(QStringLiteral("ACC模式"), 0);
    ui->ctrl_hpa_workmode_2->addItem(QStringLiteral("APC模式"), 1);

    for(int i = 0; i < 49; i++)
    {
        ui->ctrl_channel_num_1->addItem(QString("C%1").arg(13 + i), 1 + i * 2);
        ui->ctrl_channel_num_2->addItem(QString("C%1").arg(13 + i), 1 + i * 2);

        if(i < 48)
        {
            ui->ctrl_channel_num_1->addItem(QString("C%1+").arg(13 + i), 2 + i * 2);
            ui->ctrl_channel_num_2->addItem(QString("C%1+").arg(13 + i), 2 + i * 2);
        }
    }
}

void FormCombinedDev13::initTooltips()
{
    ui->ctrl_output_power_1->setProperty(MIN_VALUE, -10);
    ui->ctrl_output_power_1->setProperty(MAX_VALUE, 5);
    ui->ctrl_output_power_1->setToolTip(QString("输出功率取值范围[%1,%2]")
                                        .arg(ui->ctrl_output_power_1->property(MIN_VALUE).toDouble())
                                        .arg(ui->ctrl_output_power_1->property(MAX_VALUE).toDouble()));

    ui->ctrl_hpa_output_power_1->setProperty(MIN_VALUE, 12);
    ui->ctrl_hpa_output_power_1->setProperty(MAX_VALUE, 22);
    ui->ctrl_hpa_output_power_1->setToolTip(QString("输出功率取值范围[%1,%2]")
                                            .arg(ui->ctrl_hpa_output_power_1->property(MIN_VALUE).toInt())
                                            .arg(ui->ctrl_hpa_output_power_1->property(MAX_VALUE).toInt()));

    ui->ctrl_hpa_output_power_2->setProperty(MIN_VALUE, -10);
    ui->ctrl_hpa_output_power_2->setProperty(MAX_VALUE, 0);
    ui->ctrl_hpa_output_power_2->setToolTip(QString("输出功率取值范围[%1,%2]")
                                            .arg(ui->ctrl_hpa_output_power_2->property(MIN_VALUE).toInt())
                                            .arg(ui->ctrl_hpa_output_power_2->property(MAX_VALUE).toInt()));

    ui->ctrl_current_1->setProperty(MIN_VALUE, 100);
    ui->ctrl_current_1->setProperty(MAX_VALUE, 900);
    ui->ctrl_current_1->setToolTip(QString("高功放泵浦电流取值范围[%1,%2]")
                                   .arg(ui->ctrl_current_1->property(MIN_VALUE).toInt())
                                   .arg(ui->ctrl_current_1->property(MAX_VALUE).toInt()));

    ui->ctrl_current_2->setProperty(MIN_VALUE, 80);
    ui->ctrl_current_2->setProperty(MAX_VALUE, 600);
    ui->ctrl_current_2->setToolTip(QString("低噪放泵浦电流取值范围[%1,%2]")
                                   .arg(ui->ctrl_current_2->property(MIN_VALUE).toInt())
                                   .arg(ui->ctrl_current_2->property(MAX_VALUE).toInt()));

//    ui->ctrl_wave_compensate_1->setProperty(MIN_VALUE, 1528.734392);
//    ui->ctrl_wave_compensate_1->setProperty(MAX_VALUE, 1567.173517);
    ui->ctrl_wave_compensate_1->setProperty(MIN_VALUE, 1528.734392);
    ui->ctrl_wave_compensate_1->setProperty(MAX_VALUE, 1568.258449);
    ui->ctrl_wave_compensate_1->setToolTip(QString("发射波长取值范围[%1,%2]")
                                           .arg(ui->ctrl_wave_compensate_1->property(MIN_VALUE).toInt())
                                           .arg(ui->ctrl_wave_compensate_1->property(MAX_VALUE).toInt()));

//    ui->ctrl_wave_compensate_2->setProperty(MIN_VALUE, 1528.734392);
//    ui->ctrl_wave_compensate_2->setProperty(MAX_VALUE, 1567.173517);
        ui->ctrl_wave_compensate_2->setProperty(MIN_VALUE, 1528.734392);
        ui->ctrl_wave_compensate_2->setProperty(MAX_VALUE, 11568.258449);
    ui->ctrl_wave_compensate_2->setToolTip(QString("发射波长取值范围[%1,%2]")
                                           .arg(ui->ctrl_wave_compensate_2->property(MIN_VALUE).toInt())
                                           .arg(ui->ctrl_wave_compensate_2->property(MAX_VALUE).toInt()));

    ui->ctrl_freq_1->setProperty(MIN_VALUE, 191295);
    ui->ctrl_freq_1->setProperty(MAX_VALUE, 196105);
    ui->ctrl_freq_1->setToolTip(QString("发射频率取值范围[%1,%2]")
                                .arg(ui->ctrl_freq_1->property(MIN_VALUE).toInt())
                                .arg(ui->ctrl_freq_1->property(MAX_VALUE).toInt()));

    ui->ctrl_freq_2->setProperty(MIN_VALUE, 191295);
    ui->ctrl_freq_2->setProperty(MAX_VALUE, 196105);
    ui->ctrl_freq_2->setToolTip(QString("发射频率取值范围[%1,%2]")
                                .arg(ui->ctrl_freq_2->property(MIN_VALUE).toInt())
                                .arg(ui->ctrl_freq_2->property(MAX_VALUE).toInt()));

}

void FormCombinedDev13::initDefaultParams()
{
    if(m_machine_type == MACHINE_A){
        ui->ctrl_channel_num_1->setEditable(true);
        QCompleter* completer_1 = new QCompleter(ui->ctrl_channel_num_1->model(), this);
        completer_1->setCaseSensitivity(Qt::CaseInsensitive);
        ui->ctrl_channel_num_1->setCompleter(completer_1);
        ui->ctrl_channel_num_1->setCurrentIndex(ui->ctrl_channel_num_1->findText("C46"));

        ui->ctrl_channel_num_2->setEditable(true);
        QCompleter* completer_2 = new QCompleter(ui->ctrl_channel_num_2->model(), this);
        completer_2->setCaseSensitivity(Qt::CaseInsensitive);
        ui->ctrl_channel_num_2->setCompleter(completer_2);
        ui->ctrl_channel_num_2->setCurrentIndex(ui->ctrl_channel_num_2->findText("C18"));
    }
    else {
        ui->ctrl_channel_num_1->setEditable(true);
        QCompleter* completer_1 = new QCompleter(ui->ctrl_channel_num_1->model(), this);
        completer_1->setCaseSensitivity(Qt::CaseInsensitive);
        ui->ctrl_channel_num_1->setCompleter(completer_1);
        ui->ctrl_channel_num_1->setCurrentIndex(ui->ctrl_channel_num_1->findText("C18"));

        ui->ctrl_channel_num_2->setEditable(true);
        QCompleter* completer_2 = new QCompleter(ui->ctrl_channel_num_2->model(), this);
        completer_2->setCaseSensitivity(Qt::CaseInsensitive);
        ui->ctrl_channel_num_2->setCompleter(completer_2);
        ui->ctrl_channel_num_2->setCurrentIndex(ui->ctrl_channel_num_2->findText("C46"));
    }
}

void FormCombinedDev13::loadLastParams()
{
    ui->ctrl_module_status_1->setCurrentIndex(API_RedisClient::getInstance()->getValue("REG_STATUS_1").toInt());
    ui->ctrl_output_power_1->setText(API_RedisClient::getInstance()->getValue("REG_CONTROL_OUTPUT_POWER_1"));
    ui->ctrl_channel_num_1->setCurrentIndex(API_RedisClient::getInstance()->getValue("REG_CONTROL_CARRIER_FREQ_1").toInt());
    ui->ctrl_freq_compensate_1->setText(API_RedisClient::getInstance()->getValue("REG_CONTROL_DOPPLER_VALUE_1"));
    ui->ctrl_hpa_output_power_1->setText(API_RedisClient::getInstance()->getValue("HPA_OUTPUT_POWER"));

    ui->ctrl_module_status_2->setCurrentIndex(API_RedisClient::getInstance()->getValue("REG_STATUS_2").toInt());
    ui->ctrl_channel_num_2->setCurrentIndex(API_RedisClient::getInstance()->getValue("REG_CONTROL_CARRIER_FREQ_2").toInt());
    ui->ctrl_freq_compensate_2->setText(API_RedisClient::getInstance()->getValue("REG_CONTROL_DOPPLER_VALUE_2"));
    ui->ctrl_hpa_output_power_2->setText(API_RedisClient::getInstance()->getValue("LNA_OUTPUT_POWER"));
}

void FormCombinedDev13::setLightColor(QLabel *label, const FormCombinedDev13::LightColor color)
{
    switch (color) {
    case GRAY:
        label->setStyleSheet("border-image: url(:/icon/image/Gray_status.png);");
        break;
    case GREEN:
        label->setStyleSheet("border-image: url(:/icon/image/Green_status.png);");
        break;
    case RED:
        label->setStyleSheet("border-image: url(:/icon/image/Red_status.png);");
        break;
    case YELLOW:
        label->setStyleSheet("border-image: url(:/icon/image/Yellow_status.png);");
        break;
    }
}

double FormCombinedDev13::waveLen2freqSet(API_OpticalModule *api_optical_module, double wave_len)
{
    CacheValue queryStatus1;

    api_optical_module->getQueryStatus(queryStatus1);

    quint16 carrier_freq_1 = queryStatus1.REG_SENT_CARRIER_FREQ_1.toUShort(nullptr, 16);

    quint16 carrier_freq_2 = queryStatus1.REG_SENT_CARRIER_FREQ_2.toUShort(nullptr, 16);

    double f0 = carrier_freq_2 * 0.05 + carrier_freq_1 * 1000;

    double delta_lambda_1 = wave_len;

    double freq_offset_1 = f0 * 1000.0 * ui->comboBox_light->currentText().toInt() / (delta_lambda_1 * f0 + ui->comboBox_light->currentText().toInt()) - f0 * 1000.0;

    return freq_offset_1;
}

double FormCombinedDev13::freqSet2waveLen(API_OpticalModule *api_optical_module, double freq_set)
{
    CacheValue queryStatus1;

    api_optical_module->getQueryStatus(queryStatus1);

    quint16 carrier_freq_1 = queryStatus1.REG_SENT_CARRIER_FREQ_1.toUShort(nullptr, 16);

    quint16 carrier_freq_2 = queryStatus1.REG_SENT_CARRIER_FREQ_2.toUShort(nullptr, 16);

    double f0 = carrier_freq_2 * 0.05 + carrier_freq_1 * 1000;

    double wave_diff = ui->comboBox_light->currentText().toInt() / (f0 + 0*freq_set * 0.001) /*- ui->comboBox_light->currentText().toInt() / f0*/;

    return  wave_diff;
}

int FormCombinedDev13::findClosestIndex(const QVector<double> &array, double target)
{
    if (array.isEmpty()) {
        return -1; // 如果数组为空，返�?-1 表示未找�?
    }

    int closestIndex = 0; // 初始化为第一个元素的索引
    double smallestDifference = std::abs(array[0] - target); // 计算第一个元素与目标值的差�?

    for (int i = 1; i < array.size(); ++i) {
        double difference = std::abs(array[i] - target); // 计算当前元素与目标值的差�?

        // 如果当前元素的差值更小，更新 closestIndex �?smallestDifference
        if (difference < smallestDifference) {
            closestIndex = i;
            smallestDifference = difference;
        }
        // 如果差值相等，根据需求可以选择保留第一个或最后一个，这里选择保留第一�?
        else if (difference == smallestDifference) {
            // 如果需要保留最后一个，可以�?closestIndex 更新�?i
        }
    }

    return closestIndex;
}

void FormCombinedDev13::on_btnRecordData_clicked()
{
    if(!m_record_status){

        m_record_status = true;

        m_record_file = new QFile(QString("%1/Status_%2.csv")
                                  .arg(QCoreApplication::applicationDirPath())
                                  .arg(QDateTime::currentDateTime().toString("yyMMdd_hhmmss")));

        if(!m_record_file->open(QIODevice::WriteOnly)) return;

        QString strHead = QString("Time,modulestatus1,temp1,otuTXpower1, hpaInpower1,hpaOutpower1,ocfTHz1,ocfGHz1,"
                                  "modulestatus2,temp2,otuRXpower2,lnaInpower2,lnaOutpower2,ocfTHz2,ocfGHz2,RxOSNR2,RxEVM2,RxpreBER1,RxpreBER2,RxprbsBER1,RxprbsBER2,RxprbsBER3,RxprbsBER4,RxCFO2\n");

        m_record_file->write(strHead.toLatin1());

        m_record_file->flush();

        ui->btnRecordData->setStyleSheet("background-color: rgb(0, 255, 0);");
    }
    else {

        m_record_status = false;

        m_record_file->flush();

        m_record_file->close();

        m_record_file = nullptr;

        ui->btnRecordData->setStyleSheet("background-color: rgb(255, 255, 255);");
    }
}

void FormCombinedDev13::on_btnOpenData_clicked()
{
    if(m_record_file != nullptr)
    {
        m_record_file->flush();

        QString file_path = "file:///" + m_record_file->fileName();

        QDesktopServices::openUrl(QUrl(file_path));
    }
}

void FormCombinedDev13::on_ctrl_channel_num_1_currentIndexChanged(int index)
{
    double tmpValue = (ui->ctrl_channel_num_1->currentData(Qt::UserRole).toInt() - 1) / 2.0 + 13;

    ui->wave_length_1->setText(QString::number(ui->comboBox_light->currentText().toInt() / (tmpValue*100+190E3), 'f', 2) + "nm");
}

void FormCombinedDev13::on_ctrl_channel_num_2_currentIndexChanged(int index)
{
    double tmpValue = (ui->ctrl_channel_num_2->currentData(Qt::UserRole).toInt() - 1) / 2.0 + 13;

    ui->wave_length_2->setText(QString::number(ui->comboBox_light->currentText().toInt() / (tmpValue*100+190E3), 'f', 2) + "nm");
}

void FormCombinedDev13::on_btnSet_13_clicked()
{
    m_api_amplifier->controlSetWorkMode(1, ui->ctrl_hpa_workmode_1->currentData(Qt::UserRole).toInt());
}

void FormCombinedDev13::on_btnSet_18_clicked()
{
    m_api_amplifier->controlSetWorkMode(0, ui->ctrl_hpa_workmode_2->currentData(Qt::UserRole).toInt());
}

void FormCombinedDev13::on_btnSet_16_clicked()
{
    bool ok = false;

    qint16 current = ui->ctrl_current_1->text().toUShort(&ok);

    if(!ok)
    {
        showMessageBox(QStringLiteral("提示"), QStringLiteral("请输入合法参�?"));

        return;
    }

    if((current > ui->ctrl_current_1->property(MAX_VALUE).toInt()) ||
            (current < ui->ctrl_current_1->property(MIN_VALUE).toInt()))
    {
        QString tooltip = ui->ctrl_current_1->toolTip();

        showMessageBox(QStringLiteral("提示"), ui->ctrl_current_1->toolTip());

        return;
    }

    m_api_amplifier->controlSetCurrent(1, current);
}

void FormCombinedDev13::on_btnSet_17_clicked()
{
    bool ok = false;

    qint16 current = ui->ctrl_current_2->text().toUShort(&ok);

    if(!ok)
    {
        showMessageBox(QStringLiteral("提示"), QStringLiteral("请输入合法参�?"));

        return;
    }

    if((current > ui->ctrl_current_2->property(MAX_VALUE).toInt()) ||
            (current < ui->ctrl_current_2->property(MIN_VALUE).toInt()))
    {
        QString tooltip = ui->ctrl_current_2->toolTip();

        showMessageBox(QStringLiteral("提示"), ui->ctrl_current_2->toolTip());

        return;
    }

    m_api_amplifier->controlSetCurrent(0, current);

}

void FormCombinedDev13::on_btnSet_19_clicked()
{
    m_api_optical_module_1->controlModClose();
}

void FormCombinedDev13::on_btnSet_20_clicked()
{
    bool ok = false;

    double current = ui->ctrl_wave_compensate_1->text().toDouble(&ok);

    if(!ok)
    {
        showMessageBox(QStringLiteral("提示"), QStringLiteral("请输入合法参�?"));

        return;
    }

    if((current > ui->ctrl_wave_compensate_1->property(MAX_VALUE).toInt()) ||
            (current < ui->ctrl_wave_compensate_1->property(MIN_VALUE).toInt()))
    {
        QString tooltip = ui->ctrl_wave_compensate_1->toolTip();

        showMessageBox(QStringLiteral("提示"), ui->ctrl_wave_compensate_1->toolTip());

        return;
    }

    double lambda = ui->ctrl_wave_compensate_1->text().toDouble();

    double f_tmp = ui->comboBox_light->currentText().toInt() / lambda * 1.0;

    int index = findClosestIndex(table_list, f_tmp);

    double close_freq = table_list.at(index);

    qint16 delta_f = qRound((-close_freq + f_tmp) * 1000);

    quint16 b400_val = (index + 1 + 0xa000);

    m_api_optical_module_1->sendData("cfp2 write b400 " + QString("%1\r\n").arg(QString::number(b400_val, 16)));
    //QString ss = "cfp2 write b430 " + QString("%1\r\n").arg(QString::number(delta_f&0xffff, 16));
    m_api_optical_module_1->sendData("cfp2 write b430 " + QString("%1\r\n").arg(QString::number(delta_f & 0xffff, 16)));

    ui->ctrl_freq_1->setText(QString::number(f_tmp, 'f', 3));
}

void FormCombinedDev13::on_btnSet_21_clicked()
{
    bool ok = false;

    double current = ui->ctrl_wave_compensate_2->text().toDouble(&ok);

    if(!ok)
    {
        showMessageBox(QStringLiteral("提示"), QStringLiteral("请输入合法参�?"));

        return;
    }

    if((current > ui->ctrl_wave_compensate_2->property(MAX_VALUE).toInt()) ||
            (current < ui->ctrl_wave_compensate_2->property(MIN_VALUE).toInt()))
    {
        QString tooltip = ui->ctrl_wave_compensate_2->toolTip();

        showMessageBox(QStringLiteral("提示"), ui->ctrl_wave_compensate_2->toolTip());

        return;
    }

    double lambda = ui->ctrl_wave_compensate_2->text().toDouble(&ok);

    double f_tmp = ui->comboBox_light->currentText().toInt() / lambda * 1.0;

    int index = findClosestIndex(table_list, f_tmp);

    double close_freq = table_list.at(index);

    qint16 delta_f = qRound((-close_freq + f_tmp) * 1000);

    quint16 b400_val = (index + 1 + 0xa000);

    m_api_optical_module_2->sendData("cfp2 write b400 " + QString("%1\r\n").arg(QString::number(b400_val, 16)));

    m_api_optical_module_2->sendData("cfp2 write b430 " + QString("%1\r\n").arg(QString::number(delta_f & 0xffff, 16)));

    ui->ctrl_freq_compensate_2->setText(QString::number(delta_f));

    ui->ctrl_freq_2->setText(QString::number(f_tmp, 'f', 3));
}

void FormCombinedDev13::on_btnSet_22_clicked()
{

    bool ok = false;

    double current = ui->ctrl_freq_1->text().toDouble(&ok);

    if(!ok)
    {
        showMessageBox(QStringLiteral("提示"), QStringLiteral("请输入合法参�?"));

        return;
    }

    if((current > ui->ctrl_freq_1->property(MAX_VALUE).toInt()) ||
            (current < ui->ctrl_freq_1->property(MIN_VALUE).toInt()))
    {
        QString tooltip = ui->ctrl_freq_1->toolTip();

        showMessageBox(QStringLiteral("提示"), ui->ctrl_freq_1->toolTip());

        return;
    }

    double f_tmp = ui->ctrl_freq_1->text().toDouble();

    int index = findClosestIndex(table_list, f_tmp);

    double close_freq = table_list.at(index);

    qint16 delta_f = qRound((f_tmp-close_freq) * 1000);

    quint16 b400_val = (index + 1 + 0xa000);

    m_api_optical_module_1->sendData("cfp2 write b400 " + QString("%1\r\n").arg(QString::number(b400_val, 16)));
    m_api_optical_module_1->sendData("cfp2 write b430 " + QString("%1\r\n").arg(QString::number(delta_f& 0xffff, 16)));

    ui->ctrl_wave_compensate_1->setText(QString::number(ui->comboBox_light->currentText().toInt() / f_tmp * 1.0, 'f', 6));
}

void FormCombinedDev13::on_btnSet_25_clicked()
{
    bool ok = false;

    double current = ui->ctrl_freq_2->text().toDouble();

    if(!ok)
    {
        showMessageBox(QStringLiteral("提示"), QStringLiteral("请输入合法参�?"));

        return;
    }

    if((current > ui->ctrl_freq_2->property(MAX_VALUE).toInt()) ||
            (current < ui->ctrl_freq_2->property(MIN_VALUE).toInt()))
    {
        QString tooltip = ui->ctrl_freq_2->toolTip();

        showMessageBox(QStringLiteral("提示"), ui->ctrl_freq_2->toolTip());

        return;
    }

    double f_tmp = ui->ctrl_freq_2->text().toDouble();

    int index = findClosestIndex(table_list, f_tmp);

    double close_freq = table_list.at(index);

    qint16 delta_f = qRound((f_tmp-close_freq ) * 1000);

    quint16 b400_val = (index + 1 + 0xa000);

    m_api_optical_module_2->sendData("cfp2 write b400 " + QString("%1\r\n").arg(QString::number(b400_val, 16)));
    QTimer::singleShot(2000, this, [=](){
        m_api_optical_module_2->sendData("cfp2 write b430 " + QString("%1\r\n").arg(QString::number(delta_f & 0xffff, 16)));
    });

    ui->ctrl_freq_compensate_2->setText(QString::number(delta_f));

    ui->ctrl_wave_compensate_2->setText(QString::number(ui->comboBox_light->currentText().toInt() / f_tmp * 1.0, 'f', 6));
}

void FormCombinedDev13::on_btnSet_23_clicked()
{
    bool ok = false;

    qint16 ctrl_value = ui->ctrl_freq_compensate_1->text().toShort(&ok);

    if(!ok)
    {
        showMessageBox(QStringLiteral("提示"), QStringLiteral("请输入合法参�?"));

        return;
    }

    if((ctrl_value > ui->ctrl_freq_compensate_1->property(MAX_VALUE).toDouble()) ||
            (ctrl_value < ui->ctrl_freq_compensate_1->property(MIN_VALUE).toDouble()))
    {
        showMessageBox(QStringLiteral("提示"), ui->ctrl_freq_compensate_1->toolTip());

        return;
    }

    m_api_optical_module_1->controlCarrierFreqOffset(ctrl_value);

    API_RedisClient::getInstance()->setValue("REG_CONTROL_DOPPLER_VALUE_1", ui->ctrl_freq_compensate_1->text());

}

void FormCombinedDev13::on_btnSet_24_clicked()
{

}

void FormCombinedDev13::on_ctrl_module_status_1_activated(const QString &arg1)
{

}



void FormCombinedDev13::on_btnSet_9_clicked()
{
    bool ok = false;

    qint16 ctrl_value = ui->ctrl_freq_compensate_2->text().toShort(&ok);

    if(!ok)
    {
        showMessageBox(QStringLiteral("提示"), QStringLiteral("请输入合法参�?"));

        return;
    }

    if((ctrl_value > ui->ctrl_freq_compensate_2->property(MAX_VALUE).toDouble()) ||
            (ctrl_value < ui->ctrl_freq_compensate_2->property(MIN_VALUE).toDouble()))
    {
        showMessageBox(QStringLiteral("提示"), ui->ctrl_freq_compensate_2->toolTip());

        return;
    }

    m_api_optical_module_2->controlCarrierFreqOffset(ctrl_value);

    API_RedisClient::getInstance()->setValue("REG_CONTROL_DOPPLER_VALUE_2", ui->ctrl_freq_compensate_2->text());
}



void FormCombinedDev13::on_btnSet_27_clicked()
{
    m_api_optical_module_1->controlLaserOn();
}

void FormCombinedDev13::on_btnSet_26_clicked()
{
    qint16 start_bin = ui->ctrl_start_bin->text().toShort();
//    qint16 end_bin = ui->ctrl_end_bin->text().toShort();
//    qint16 freq_vary = ui->ctrl_freq_vary->text().toShort();

    m_api_optical_module_1->controlStartScan(start_bin, 0, 0);
}

void FormCombinedDev13::on_btnSet_28_clicked()
{
    m_api_optical_module_1->controlLaserOff();
}

void FormCombinedDev13::on_sta_scanstart_linkActivated(const QString &link)
{

}

void FormCombinedDev13::on_btnSet_5_clicked()
{
    qint32 start_freq = ui->ctrl_start_freq->text().toInt();
    qint32 end_freq = ui->ctrl_end_freq->text().toInt();
    qint32 scan_rate = ui->ctrl_scan_rate->text().toInt();

    qint32 correct_scan_rate = qCeil(scan_rate * 1.7);
    quint32 scan_num = qAbs(round((end_freq - start_freq) / correct_scan_rate));

    m_api_optical_module_1->controlScanFreq(start_freq, correct_scan_rate, scan_num);
}

void FormCombinedDev13::on_ctrl_start_freq_cursorPositionChanged(int arg1, int arg2)
{

}




void FormCombinedDev13::on_comboBox_light_currentIndexChanged(int index)
{
    static int number=0;
    if(number!=0){
      QString filepath="my.txt";
      QFile file(filepath);
      if(!file.open(QIODevice::WriteOnly|QIODevice::Text|QIODevice::Truncate)){
        return;
      }
      QTextStream out(&file);
      out<<index;
      file.close();
    }
    number++;
}
