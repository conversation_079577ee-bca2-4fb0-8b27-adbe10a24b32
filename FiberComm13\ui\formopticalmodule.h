#ifndef FORMOPTICALMODULE_H
#define FORMOPTICALMODULE_H

#include <QWidget>
#include <QFile>
#include <QLabel>

#include "api_opticalmodule.h"

namespace Ui {
class FormOpticalModule;
}

class FormOpticalModule : public QWidget
{
    Q_OBJECT

public:
    typedef enum LIGHT_COLOR
    {
        GRAY, GREEN, RED, YELLOW
    }LightColor;

    explicit FormOpticalModule(const QString name, API_OpticalModule* api_optical_module = nullptr, QWidget *parent = nullptr);
    ~FormOpticalModule();

private slots:

    void on_btnTestCommand_clicked();

    void on_btnSet_b010_clicked();

    void on_btnSet_b410_clicked();

    void on_btnSet_b400_clicked();

    void on_btnSet_b430_clicked();

    void on_btnRecord_clicked();

    void on_btnRecord_2_clicked();

private:
    void init();

    void displayModuleStatus();

    void displayOutputPower();

    void displayInputPower();

    void displaySNR();

    void displayCarrierFreq();

    void displayCarrierFreqOffset();

    void displayPRBSBitCount();

    void recordStatus();

    void setLightColor(QLabel* label, const LightColor color);

private:
    Ui::FormOpticalModule *ui;

    API_OpticalModule* m_api_optical_module = nullptr;

    bool m_record_status = false;

    QFile* m_record_file = nullptr;

    QString m_name = "";
};

#endif // FORMOPTICALMODULE_H
