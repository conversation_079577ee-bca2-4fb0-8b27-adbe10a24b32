<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FormOpticalModule</class>
 <widget class="QWidget" name="FormOpticalModule">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1020</width>
    <height>716</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2" stretch="1,1,0,0">
   <item>
    <widget class="QGroupBox" name="groupBox_5">
     <property name="font">
      <font>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="title">
      <string>状态参数</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4" stretch="0,1">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,1,0,2,1,0,2,1,0,2,1">
        <item>
         <widget class="QLabel" name="label_2">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>功耗状态：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="sta_power_status">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/image/image/Gray_Round32.bmp);</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_7">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_5">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>传输通道状态：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="sta_switch_status">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/image/image/Gray_Round32.bmp);</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_6">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_19">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>传输配置状态：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="sta_config_status">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/image/image/Gray_Round32.bmp);</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_8">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_21">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>功率放大器工作状态：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="sta_amplifier_status">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/image/image/Gray_Round32.bmp);</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout" columnstretch="1,1,0,1,1">
        <item row="2" column="0">
         <widget class="QLabel" name="label_10">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>频偏估计值(MHz)：</string>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="1" column="1">
         <widget class="QLabel" name="snr">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="4">
         <widget class="QLabel" name="output_power">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QLabel" name="label_3">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输出功率(dBm)：</string>
          </property>
         </widget>
        </item>
        <item row="2" column="3">
         <widget class="QLabel" name="label_18">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>误码率统计：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_6">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输入功率(dBm)：</string>
          </property>
         </widget>
        </item>
        <item row="1" column="4">
         <widget class="QLabel" name="sent_power">
          <property name="minimumSize">
           <size>
            <width>180</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLabel" name="freq_offset">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLabel" name="input_power">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="1" column="3">
         <widget class="QLabel" name="label_16">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>载波频率(GHz)：</string>
          </property>
         </widget>
        </item>
        <item row="2" column="4">
         <widget class="QLabel" name="bit_count">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_8">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>接收光信噪比(dB)：</string>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QGridLayout" name="gridLayout_6" columnstretch="1,1">
     <item row="0" column="0">
      <widget class="QGroupBox" name="groupBox">
       <property name="font">
        <font>
         <pointsize>16</pointsize>
        </font>
       </property>
       <property name="title">
        <string>状态控制</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <layout class="QGridLayout" name="gridLayout_2" columnstretch="1,1,0,1">
          <item row="0" column="0">
           <widget class="QLabel" name="label_11">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>状态：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QComboBox" name="ctrl_module_status">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>160</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QPushButton" name="btnSet_b010">
            <property name="minimumSize">
             <size>
              <width>140</width>
              <height>40</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>设置</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <spacer name="horizontalSpacer_10">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QGroupBox" name="groupBox_2">
       <property name="font">
        <font>
         <pointsize>16</pointsize>
        </font>
       </property>
       <property name="title">
        <string>输出功率控制</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <layout class="QGridLayout" name="gridLayout_3" columnstretch="1,1,0,1">
          <item row="0" column="0">
           <widget class="QLabel" name="label_12">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>输出功率(dBm)：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QPushButton" name="btnSet_b410">
            <property name="minimumSize">
             <size>
              <width>140</width>
              <height>40</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>设置</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLineEdit" name="ctrl_output_power">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>160</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>16</pointsize>
             </font>
            </property>
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <spacer name="horizontalSpacer_11">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QGroupBox" name="groupBox_3">
       <property name="font">
        <font>
         <pointsize>16</pointsize>
        </font>
       </property>
       <property name="title">
        <string>载波频率控制</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <layout class="QGridLayout" name="gridLayout_4" columnstretch="1,1,1,1,1">
          <item row="0" column="3">
           <widget class="QLineEdit" name="ctrl_channel_num">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>40</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>16</pointsize>
             </font>
            </property>
            <property name="text">
             <string>18</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="label_13">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>频率间隔：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QComboBox" name="ctrl_freq_interval">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>16</pointsize>
             </font>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QPushButton" name="btnSet_b400">
            <property name="minimumSize">
             <size>
              <width>140</width>
              <height>40</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>设置</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="label_14">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>通道个数：</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QGroupBox" name="groupBox_4">
       <property name="font">
        <font>
         <pointsize>16</pointsize>
        </font>
       </property>
       <property name="title">
        <string>频率预补偿控制</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <layout class="QGridLayout" name="gridLayout_5" columnstretch="1,1,0,1">
          <item row="0" column="0">
           <widget class="QLabel" name="label_23">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>频率补偿(MHz)：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QPushButton" name="btnSet_b430">
            <property name="minimumSize">
             <size>
              <width>140</width>
              <height>40</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>设置</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLineEdit" name="ctrl_freq_compensate">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>160</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>16</pointsize>
             </font>
            </property>
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <spacer name="horizontalSpacer_12">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_6">
     <property name="font">
      <font>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="title">
      <string>控制台</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_6">
        <item>
         <widget class="QLabel" name="label_17">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>执行命令：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="control_command">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>40</height>
           </size>
          </property>
          <property name="text">
           <string>1111</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_14">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="btnTestCommand">
          <property name="minimumSize">
           <size>
            <width>140</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>设置</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_8">
        <item>
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="btnRecord">
          <property name="minimumSize">
           <size>
            <width>140</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>记录状态</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnRecord_2">
          <property name="minimumSize">
           <size>
            <width>140</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>打开状态表</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_7">
     <property name="font">
      <font>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="title">
      <string>执行结果</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_7">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_5">
        <item>
         <widget class="QLabel" name="label_15">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>执行结果：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="control_result">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>40</height>
           </size>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
