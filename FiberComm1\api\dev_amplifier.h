#ifndef DEV_AMPLIFIER_H
#define DEV_AMPLIFIER_H

#include <QtGlobal>
#include <QByteArray>

#pragma pack(1)

namespace Amplifier
{
const static quint16 CMDID_QUERY_UNIT_REQUEST = 0x0000;
const static quint16 CMDID_QUERY_UNIT_RESPONSE = 0x0000;
const static quint16 CMDID_SET_SWITCH_REQUEST = 0xA001;
const static quint16 CMDID_SET_WORK_MODE_REQUEST = 0xA002;
const static quint16 CMDID_SET_CURRENT_REQUEST = 0xA003;
const static quint16 CMDID_SET_OUTPUT_POWER_REQUEST = 0xA004;
const static quint16 CMDID_GET_ONLINE_STATUS_RESPONSE = 0x0007;
const static quint16 CMDID_SET_ONLINE_STATUS_REQUEST = 0xA007;

typedef struct PROTOCOL_AMPLIFIER
{
    quint16 begin;  //起始位
    quint16 addr;   //地址
    quint16 cmdid;  //指令
    quint16 len;    //长度

    PROTOCOL_AMPLIFIER()
    {
        this->begin = 0x55aa;
        this->addr = 0xffff;
    }
}ProtocolAmplifier;

//查询通道开关状态0x0000
typedef struct QUERY_UNIT
{
    qint16 module_temp;             //模块温度,单位0.1℃
    qint16 module_input_vol;        //模块输入电压,1mv
    /**
     * @brief status
     * BIT7:低噪声模式
     * BIT6:高功率模式
     * 0为ACC,1为APC
     * BIT5:低噪声开关
     * BIT4:高功率开关
     * 0为关,1为开
     */
    quint8 module_status;           //模块状态
    qint16 low_noise_input_freq;    //低噪声输入功率
    qint16 high_power_input_freq;   //高功率输入功率
    qint16 low_noise_output_freq;   //低噪声输出功率
    qint16 high_power_output_freq;  //高功率输出功率
    qint16 pump_current_1;          //泵浦1电流
    qint16 pump_temp_1;             //泵浦1温度
    qint16 pump_bak_current_1;      //泵浦1背光电流
    qint16 pump_tec_current_1;      //泵浦1TEC电流
    qint16 pump_current_2;          //泵浦2电流
    qint16 pump_temp_2;             //泵浦2温度
    qint16 pump_bak_current_2;      //泵浦2背光电流
    qint16 pump_tec_current_2;      //泵浦2TEC电流
    /**
     * @brief module_warn
     * BIT7:模块温度高告警
     * BIT6:模块温度低告警
     * BIT5:模块输入电压高告警
     * BIT4:模块输入电压低告警
     * 0为正常,1为告警,其他为保留
     */
    quint8 module_warn;             //模块告警
    /**
     * @brief power_warn
     * BIT7:低噪声输入功率高告警
     * BIT6:低噪声输入功率低告警
     * BIT5:高噪声输入功率高告警
     * BIT4:高噪声输入功率低告警
     * BIT3:低噪声输出功率高告警
     * BIT2:低噪声输出功率低告警
     * BIT1:高噪声输出功率高告警
     * BIT0:高噪声输出功率低告警
     * 0为正常,1为告警,其他为保留
     */
    quint8 power_warn;              //功率告警
    /**
     * @brief power_warn
     * BIT7:泵浦1电流高告警
     * BIT6:泵浦1电流低告警
     * BIT5:泵浦1温度高告警
     * BIT4:泵浦1温度低告警
     * BIT3:泵浦1背光高告警
     * BIT2:泵浦1背光低告警
     * BIT1:泵浦1TEC电流高告警
     * BIT0:泵浦1TEC电流低告警
     * 0为正常,1为告警,其他为保留
     */
    quint8 pump_warn_1;              //泵浦1告警
    /**
     * @brief power_warn
     * BIT7:泵浦2电流高告警
     * BIT6:泵浦2电流低告警
     * BIT5:泵浦2温度高告警
     * BIT4:泵浦2温度低告警
     * BIT3:泵浦2背光高告警
     * BIT2:泵浦2背光低告警
     * BIT1:泵浦2TEC电流高告警
     * BIT0:泵浦2TEC电流低告警
     * 0为正常,1为告警,其他为保留
     */
    quint8 pump_warn_2;              //泵浦2告警
    char manufacturer[32];           //制造商名称
    char module_seq[32];             //模块序列号
    char version_id[32];             //固件版本
    char module_produced_date[32];   //模块生产日期
}QueryUnitResponse;

//设置通道开关状态0xA001
typedef struct SET_CHANNEL_SWITCH
{
    quint8 channel_id;      //通道号,0低噪声通道;1高功率通道
    quint8 switch_status;   //设置开关状态,0为关;1为开
}SetSwitchRequest;

//设置通道工作模式0xA002
typedef struct SET_CHANNEL_WORK_MODE
{
    quint8 channel_id;      //通道号,0低噪声通道;1高功率通道
    quint8 work_mode;       //设置工作模式,0为ACC模式;1APC模式
}SetWorkModeRequest;

//设置泵浦电流值0xA003
typedef struct SET_PUMP_CURRENT
{
    quint8 pump_id;         //泵浦编号,0泵浦1;1泵浦2
    quint16 current;         //设置电流值,单位1mA
}SetCurrentRequest;

//设置通道输出功率值0xA004
typedef struct SET_CHANNEL_OUTPUT_POWER
{
    quint8 channel_id;      //通道号,0低噪声通道;1高功率通道
    qint16 output_power;    //功率设置值,单位0.01dBm
}SetOutputPowerRequest;

//查询上电状态0x0007
typedef struct GET_ONLINE_STATUS
{
    quint8 online_status;      //上电状态,0xAA为出场状态(关泵;数据不保存,恢复出厂状态)0x55为保存设置(关泵;数据恢复上一次掉电前的状态)
}GetOnlineStatusResponse;

//设置上电状态0xA007
typedef struct SET_ONLINE_STATUS
{
    quint8 online_status;      //上电状态,0xAA为出场状态(关泵;数据不保存,恢复出厂状态)0x55为保存设置(关泵;数据恢复上一次掉电前的状态)
}SetOnlineStatusRequest;



static quint8 check_data_xor(const QByteArray& buf)
{
    quint8 res = 0;

    for(quint16 i = 0; i < buf.size(); i++)
    {
        res ^= (buf[i]);
    }

    return res;
}

//static quint16 reverse(const quint16 srcValue)
//{
//    QByteArray tmpByte;

//    tmpByte.resize(2);

//    ::memcpy(tmpByte.data(), &srcValue, sizeof(quint16));

//    std::reverse(tmpByte.begin(), tmpByte.end());

//    quint16 dstValue = 0;

//    ::memcpy(&dstValue, tmpByte.data(), sizeof (quint16));

//    return dstValue;
//}

template <typename T>

static T reverse(const T srcValue)
{
    QByteArray tmpByte;

    tmpByte.resize(sizeof(T));

    ::memcpy(tmpByte.data(), &srcValue, sizeof(T));

    std::reverse(tmpByte.begin(), tmpByte.end());

    T dstValue = 0;

    ::memcpy(&dstValue, tmpByte.data(), sizeof(T));

    return dstValue;
}
}

#pragma pack()


#endif // DEV_AMPLIFIER_H
