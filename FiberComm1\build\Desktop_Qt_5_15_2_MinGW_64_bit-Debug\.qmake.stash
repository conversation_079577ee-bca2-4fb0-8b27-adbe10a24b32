QMAKE_CXX.QT_COMPILER_STDCXX = 201402L
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 8
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 1
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 0
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    D:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ \
    D:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 \
    D:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward \
    D:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include \
    D:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed \
    D:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include
QMAKE_CXX.LIBDIRS = \
    D:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0 \
    D:/soft/QT/Qt/Tools/mingw810_64/lib/gcc \
    D:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/lib \
    D:/soft/QT/Qt/Tools/mingw810_64/lib
