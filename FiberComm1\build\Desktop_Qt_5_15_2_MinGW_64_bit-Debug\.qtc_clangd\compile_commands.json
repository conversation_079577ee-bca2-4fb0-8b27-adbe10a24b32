[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm\\api\\api_amplifier.cpp"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/api/api_amplifier.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm\\api\\api_opticalmodule.cpp"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/api/api_opticalmodule.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm\\api\\api_redisclient.cpp"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/api/api_redisclient.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm\\api\\qttelnet.cpp"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/api/qttelnet.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm\\main.cpp"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm\\mainwindow.cpp"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm\\tool\\logs.cpp"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/tool/logs.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm\\tool\\netconfigsettings.cpp"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/tool/netconfigsettings.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm\\ui\\formamplifier.cpp"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/ui/formamplifier.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm\\ui\\formcombineddev.cpp"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/ui/formcombineddev.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm\\ui\\formopticalmodule.cpp"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/ui/formopticalmodule.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include\\adapters\\qt.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/QtHiRedis_Lib_PATH/include/adapters/qt.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\api\\api_redisclient.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/api/api_redisclient.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\api\\api_amplifier.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/api/api_amplifier.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\api\\api_opticalmodule.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/api/api_opticalmodule.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\api\\dev_amplifier.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/api/dev_amplifier.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\api\\dev_opticalmodule.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/api/dev_opticalmodule.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\api\\qttelnet.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/api/qttelnet.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\mainwindow.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\tool\\global.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/tool/global.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\tool\\logs.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/tool/logs.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\tool\\netconfigsettings.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/tool/netconfigsettings.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\ui\\formamplifier.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/ui/formamplifier.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\ui\\formcombineddev.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/ui/formcombineddev.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\ui\\formopticalmodule.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/ui/formopticalmodule.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\ui_mainwindow.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/ui_mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\ui_formcombineddev.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/ui_formcombineddev.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\ui_formopticalmodule.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/ui_formopticalmodule.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm", "-IE:\\work\\code\\QtCode\\FiberComm\\api", "-IE:\\work\\code\\QtCode\\FiberComm\\ui", "-IE:\\work\\code\\QtCode\\FiberComm\\tool", "-IE:\\work\\code\\QtCode\\FiberComm\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\ui_formamplifier.h"], "directory": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/ui_formamplifier.h"}]