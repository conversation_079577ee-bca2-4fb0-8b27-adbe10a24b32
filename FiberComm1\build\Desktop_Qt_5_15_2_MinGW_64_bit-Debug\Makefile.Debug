#############################################################################
# Makefile for building: FiberComm
# Generated by qmake (3.1) (Qt 5.15.2)
# Project:  ..\..\FiberComm.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_DEPRECATED_WARNINGS -DQT_QML_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SERIALPORT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I..\..\..\FiberComm -I. -I..\..\api -I..\..\ui -I..\..\tool -I..\..\QtHiRedis_Lib_PATH\include -ID:\soft\QT\Qt\5.15.2\mingw81_64\include -ID:\soft\QT\Qt\5.15.2\mingw81_64\include\QtWidgets -ID:\soft\QT\Qt\5.15.2\mingw81_64\include\QtGui -ID:\soft\QT\Qt\5.15.2\mingw81_64\include\QtANGLE -ID:\soft\QT\Qt\5.15.2\mingw81_64\include\QtSerialPort -ID:\soft\QT\Qt\5.15.2\mingw81_64\include\QtNetwork -ID:\soft\QT\Qt\5.15.2\mingw81_64\include\QtCore -Idebug -I. -I/include -ID:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        -lwsock32 -lws2_32 -LE:\work\code\QtCode\FiberComm\QtHiRedis_Lib_PATH\libs -lQtHiRedis D:\soft\QT\Qt\5.15.2\mingw81_64\lib\libQt5Widgets.a D:\soft\QT\Qt\5.15.2\mingw81_64\lib\libQt5Gui.a D:\soft\QT\Qt\5.15.2\mingw81_64\lib\libQt5SerialPort.a D:\soft\QT\Qt\5.15.2\mingw81_64\lib\libQt5Network.a D:\soft\QT\Qt\5.15.2\mingw81_64\lib\libQt5Core.a debug\FiberComm_resource_res.o  -lmingw32 D:\soft\QT\Qt\5.15.2\mingw81_64\lib\libqtmain.a -LC:\openssl\lib -LC:\Utils\my_sql\mysql-5.7.25-winx64\lib -LC:\Utils\postgresql\pgsql\lib -lshell32 
QMAKE         = D:\soft\QT\Qt\5.15.2\mingw81_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\soft\QT\Qt\5.15.2\mingw81_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\soft\QT\Qt\5.15.2\mingw81_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = debug\FiberComm_resource_res.o
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = ..\..\api\api_amplifier.cpp \
		..\..\api\api_opticalmodule.cpp \
		..\..\api\api_redisclient.cpp \
		..\..\api\qttelnet.cpp \
		..\..\main.cpp \
		..\..\mainwindow.cpp \
		..\..\tool\logs.cpp \
		..\..\tool\netconfigsettings.cpp \
		..\..\ui\formamplifier.cpp \
		..\..\ui\formcombineddev.cpp \
		..\..\ui\formopticalmodule.cpp debug\qrc_resource.cpp \
		debug\moc_qt.cpp \
		debug\moc_api_redisclient.cpp \
		debug\moc_api_amplifier.cpp \
		debug\moc_api_opticalmodule.cpp \
		debug\moc_qttelnet.cpp \
		debug\moc_mainwindow.cpp \
		debug\moc_formamplifier.cpp \
		debug\moc_formcombineddev.cpp \
		debug\moc_formopticalmodule.cpp
OBJECTS       = debug/api_amplifier.o \
		debug/api_opticalmodule.o \
		debug/api_redisclient.o \
		debug/qttelnet.o \
		debug/main.o \
		debug/mainwindow.o \
		debug/logs.o \
		debug/netconfigsettings.o \
		debug/formamplifier.o \
		debug/formcombineddev.o \
		debug/formopticalmodule.o \
		debug/qrc_resource.o \
		debug/moc_qt.o \
		debug/moc_api_redisclient.o \
		debug/moc_api_amplifier.o \
		debug/moc_api_opticalmodule.o \
		debug/moc_qttelnet.o \
		debug/moc_mainwindow.o \
		debug/moc_formamplifier.o \
		debug/moc_formcombineddev.o \
		debug/moc_formopticalmodule.o

DIST          =  ..\..\QtHiRedis_Lib_PATH\include\adapters\qt.h \
		..\..\api\api_redisclient.h \
		..\..\api\api_amplifier.h \
		..\..\api\api_opticalmodule.h \
		..\..\api\dev_amplifier.h \
		..\..\api\dev_opticalmodule.h \
		..\..\api\qttelnet.h \
		..\..\mainwindow.h \
		..\..\tool\global.h \
		..\..\tool\logs.h \
		..\..\tool\netconfigsettings.h \
		..\..\ui\formamplifier.h \
		..\..\ui\formcombineddev.h \
		..\..\ui\formopticalmodule.h ..\..\api\api_amplifier.cpp \
		..\..\api\api_opticalmodule.cpp \
		..\..\api\api_redisclient.cpp \
		..\..\api\qttelnet.cpp \
		..\..\main.cpp \
		..\..\mainwindow.cpp \
		..\..\tool\logs.cpp \
		..\..\tool\netconfigsettings.cpp \
		..\..\ui\formamplifier.cpp \
		..\..\ui\formcombineddev.cpp \
		..\..\ui\formopticalmodule.cpp
QMAKE_TARGET  = FiberComm
DESTDIR        = ..\..\bin_d\ #avoid trailing-slash linebreak
TARGET         = FiberComm.exe
DESTDIR_TARGET = ..\..\bin_d\FiberComm.exe

####### Build rules

first: all
all: Makefile.Debug  ../../bin_d/FiberComm.exe

../../bin_d/FiberComm.exe: D:/soft/QT/Qt/5.15.2/mingw81_64/lib/libQt5Widgets.a D:/soft/QT/Qt/5.15.2/mingw81_64/lib/libQt5Gui.a D:/soft/QT/Qt/5.15.2/mingw81_64/lib/libQt5SerialPort.a D:/soft/QT/Qt/5.15.2/mingw81_64/lib/libQt5Network.a D:/soft/QT/Qt/5.15.2/mingw81_64/lib/libQt5Core.a D:/soft/QT/Qt/5.15.2/mingw81_64/lib/libqtmain.a ui_mainwindow.h ui_formamplifier.h ui_formcombineddev.h ui_formopticalmodule.h $(OBJECTS) debug/FiberComm_resource_res.o
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @object_script.FiberComm.Debug  $(LIBS)

debug/FiberComm_resource_res.o: FiberComm_resource.rc
	windres -i FiberComm_resource.rc -o debug\FiberComm_resource_res.o --include-dir=. $(DEFINES)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug ..\..\FiberComm.pro -spec win32-g++ "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) FiberComm.zip $(SOURCES) $(DIST) ..\..\FiberComm.pro D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\spec_pre.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\qdevice.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\device_config.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\common\sanitize.conf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\common\gcc-base.conf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\common\g++-base.conf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\common\angle.conf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\win32\windows_vulkan_sdk.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\common\windows-vulkan.conf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\common\g++-win32.conf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\common\windows-desktop.conf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\qconfig.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3danimation.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3danimation_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dcore.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dcore_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dextras.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dextras_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dinput.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dinput_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dlogic.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dlogic_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquick.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquick_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickanimation.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickextras.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickextras_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickinput.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickinput_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickrender.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickrender_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickscene2d.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3drender.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3drender_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_accessibility_support_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axbase.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axbase_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axcontainer.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axcontainer_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axserver.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axserver_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_bluetooth.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_bluetooth_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_bodymovin_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_bootstrap_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_charts.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_charts_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_concurrent.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_concurrent_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_core.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_core_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_datavisualization.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_datavisualization_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_dbus.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_dbus_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_designer.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_designer_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_designercomponents_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_edid_support_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_egl_support_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_fb_support_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_fontdatabase_support_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_gamepad.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_gamepad_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_gui.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_gui_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_help.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_help_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_insighttracker.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_insighttracker_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_insighttrackerqml.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_insighttrackerqml_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_location.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_location_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_multimedia.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_multimedia_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_multimediawidgets.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_network.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_network_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_networkauth.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_networkauth_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_nfc.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_nfc_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_opengl.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_opengl_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_openglextensions.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_openglextensions_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_packetprotocol_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_platformcompositor_support_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_positioning.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_positioning_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_positioningquick.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_positioningquick_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_printsupport.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_printsupport_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_purchasing.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_purchasing_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qml.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qml_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmldebug_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmldevtools_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmlmodels.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmlmodels_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmltest.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmltest_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmlworkerscript.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick3d.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick3d_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick3dassetimport.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick3drender.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick3drender_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick3druntimerender.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick3dutils.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick3dutils_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickcontrols2.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickparticles_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickshapes_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quicktemplates2.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickwidgets.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_remoteobjects.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_remoteobjects_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_repparser.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_repparser_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_script.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_script_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_scripttools.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_scripttools_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_scxml.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_scxml_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_sensors.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_sensors_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_serialbus.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_serialbus_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_serialport.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_serialport_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_sql.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_sql_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_svg.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_svg_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_testlib.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_testlib_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_texttospeech.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_texttospeech_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_theme_support_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_uiplugin.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_uitools.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_uitools_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_virtualkeyboard.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_virtualkeyboard_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_vulkan_support_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_webchannel.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_webchannel_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_websockets.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_websockets_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_widgets.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_widgets_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_windowsuiautomation_support_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_winextras.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_winextras_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_xml.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_xml_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_xmlpatterns.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_xmlpatterns_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\modules\qt_lib_zlib_private.pri D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\qt_functions.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\qt_config.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\win32-g++\qmake.conf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\spec_post.prf .qmake.stash D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\exclusive_builds.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\toolchain.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\default_pre.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\win32\default_pre.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\resolve_config.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\exclusive_builds_post.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\default_post.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\build_pass.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\qml_debug.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\precompile_header.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\warn_on.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\qt.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\resources_functions.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\resources.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\moc.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\win32\opengl.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\uic.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\qmake_use.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\file_copies.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\win32\windows.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\testcase_targets.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\exceptions.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\yacc.prf D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\lex.prf ..\..\FiberComm.pro ..\..\resource.qrc D:\soft\QT\Qt\5.15.2\mingw81_64\lib\Qt5Widgets.prl D:\soft\QT\Qt\5.15.2\mingw81_64\lib\Qt5Gui.prl D:\soft\QT\Qt\5.15.2\mingw81_64\lib\Qt5SerialPort.prl D:\soft\QT\Qt\5.15.2\mingw81_64\lib\Qt5Network.prl D:\soft\QT\Qt\5.15.2\mingw81_64\lib\Qt5Core.prl D:\soft\QT\Qt\5.15.2\mingw81_64\lib\qtmain.prl   ..\..\resource.qrc D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\data\dummy.cpp ..\..\QtHiRedis_Lib_PATH\include\adapters\qt.h ..\..\api\api_redisclient.h ..\..\api\api_amplifier.h ..\..\api\api_opticalmodule.h ..\..\api\dev_amplifier.h ..\..\api\dev_opticalmodule.h ..\..\api\qttelnet.h ..\..\mainwindow.h ..\..\tool\global.h ..\..\tool\logs.h ..\..\tool\netconfigsettings.h ..\..\ui\formamplifier.h ..\..\ui\formcombineddev.h ..\..\ui\formopticalmodule.h  ..\..\api\api_amplifier.cpp ..\..\api\api_opticalmodule.cpp ..\..\api\api_redisclient.cpp ..\..\api\qttelnet.cpp ..\..\main.cpp ..\..\mainwindow.cpp ..\..\tool\logs.cpp ..\..\tool\netconfigsettings.cpp ..\..\ui\formamplifier.cpp ..\..\ui\formcombineddev.cpp ..\..\ui\formopticalmodule.cpp ..\..\mainwindow.ui ..\..\ui\formamplifier.ui ..\..\ui\formcombineddev.ui ..\..\ui\formopticalmodule.ui    

clean: compiler_clean 
	-$(DEL_FILE) debug\api_amplifier.o debug\api_opticalmodule.o debug\api_redisclient.o debug\qttelnet.o debug\main.o debug\mainwindow.o debug\logs.o debug\netconfigsettings.o debug\formamplifier.o debug\formcombineddev.o debug\formopticalmodule.o debug\qrc_resource.o debug\moc_qt.o debug\moc_api_redisclient.o debug\moc_api_amplifier.o debug\moc_api_opticalmodule.o debug\moc_qttelnet.o debug\moc_mainwindow.o debug\moc_formamplifier.o debug\moc_formcombineddev.o debug\moc_formopticalmodule.o
	-$(DEL_FILE) debug\FiberComm_resource_res.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: debug/qrc_resource.cpp
compiler_rcc_clean:
	-$(DEL_FILE) debug\qrc_resource.cpp
debug/qrc_resource.cpp: ../../resource.qrc \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/rcc.exe \
		../../image/StartRecord.png \
		../../image/bg11.jpg \
		../../image/Red_status.png \
		../../image/bgt2.png \
		../../image/arrow_right.png \
		../../image/StopRecord.png \
		../../image/bg2.jpg \
		../../image/jiance.png \
		../../image/Green_Round32.bmp \
		../../image/kaishijilu.png \
		../../image/pinlv-green.png \
		../../image/bingo.png \
		../../image/settingGray1.png \
		../../image/pilotLamp-red.png \
		../../image/jieshou.png \
		../../image/fasong-gray.png \
		../../image/recordFilewhite.png \
		../../image/ui.qss \
		../../image/stop-bai.png \
		../../image/jianceBlue.png \
		../../image/bg1.png \
		../../image/fasongduan.png \
		../../image/bgt.png \
		../../image/guanbi-white.png \
		../../image/pilotLampBlue.png \
		../../image/pinlv-gray.png \
		../../image/pilotLamp.png \
		../../image/Red_Round32.bmp \
		../../image/yuan.png \
		../../image/setting.png \
		../../image/pinlv-red.png \
		../../image/fasong-white.png \
		../../image/arrow_down.png \
		../../image/Gray_status.png \
		../../image/statusBlue.png \
		../../image/Red_Round32.png \
		../../image/recordFileGray1.png \
		../../image/bg8.png \
		../../image/yuan-up.png \
		../../image/Yellow_status.png \
		../../image/detail1.png \
		../../image/Gray_Round32.bmp \
		../../image/jieshouduan.png \
		../../image/detail.png \
		../../image/Stop.png \
		../../image/bg7.png \
		../../image/Green_status.png \
		../../image/settingBlue.png \
		../../image/pilotLampGreen.png \
		../../image/pinlv-white.png \
		../../image/status.png \
		../../image/yuan-down.png \
		../../image/pinlv-blue.png \
		../../image/Yellow_Round32.bmp \
		../../image/group2.png \
		../../image/arrow_left.png \
		../../image/pilotLampGray.png \
		../../image/group.png
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\rcc.exe -name resource ..\..\resource.qrc -o debug\qrc_resource.cpp

compiler_moc_predefs_make_all: debug/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) debug\moc_predefs.h
debug/moc_predefs.h: D:/soft/QT/Qt/5.15.2/mingw81_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o debug\moc_predefs.h D:\soft\QT\Qt\5.15.2\mingw81_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: debug/moc_qt.cpp debug/moc_api_redisclient.cpp debug/moc_api_amplifier.cpp debug/moc_api_opticalmodule.cpp debug/moc_qttelnet.cpp debug/moc_mainwindow.cpp debug/moc_formamplifier.cpp debug/moc_formcombineddev.cpp debug/moc_formopticalmodule.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc_qt.cpp debug\moc_api_redisclient.cpp debug\moc_api_amplifier.cpp debug\moc_api_opticalmodule.cpp debug\moc_qttelnet.cpp debug\moc_mainwindow.cpp debug\moc_formamplifier.cpp debug\moc_formcombineddev.cpp debug\moc_formopticalmodule.cpp
debug/moc_qt.cpp: ../../QtHiRedis_Lib_PATH/include/adapters/qt.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSocketNotifier \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsocketnotifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		../../QtHiRedis_Lib_PATH/include/async.h \
		../../QtHiRedis_Lib_PATH/include/hiredis.h \
		../../QtHiRedis_Lib_PATH/include/read.h \
		../../QtHiRedis_Lib_PATH/include/sds.h \
		../../QtHiRedis_Lib_PATH/include/alloc.h \
		debug/moc_predefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/moc.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/soft/QT/Qt/5.15.2/mingw81_64/mkspecs/win32-g++ -IE:/work/code/QtCode/FiberComm -IE:/work/code/QtCode/FiberComm/api -IE:/work/code/QtCode/FiberComm/ui -IE:/work/code/QtCode/FiberComm/tool -IE:/work/code/QtCode/FiberComm/QtHiRedis_Lib_PATH/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtANGLE -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore -I. -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include ..\..\QtHiRedis_Lib_PATH\include\adapters\qt.h -o debug\moc_qt.cpp

debug/moc_api_redisclient.cpp: ../../api/api_redisclient.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		../../QtHiRedis_Lib_PATH/include/adapters/qt.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSocketNotifier \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsocketnotifier.h \
		../../QtHiRedis_Lib_PATH/include/async.h \
		../../QtHiRedis_Lib_PATH/include/hiredis.h \
		../../QtHiRedis_Lib_PATH/include/read.h \
		../../QtHiRedis_Lib_PATH/include/sds.h \
		../../QtHiRedis_Lib_PATH/include/alloc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutexLocker \
		debug/moc_predefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/moc.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/soft/QT/Qt/5.15.2/mingw81_64/mkspecs/win32-g++ -IE:/work/code/QtCode/FiberComm -IE:/work/code/QtCode/FiberComm/api -IE:/work/code/QtCode/FiberComm/ui -IE:/work/code/QtCode/FiberComm/tool -IE:/work/code/QtCode/FiberComm/QtHiRedis_Lib_PATH/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtANGLE -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore -I. -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include ..\..\api\api_redisclient.h -o debug\moc_api_redisclient.cpp

debug/moc_api_amplifier.cpp: ../../api/api_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/QSerialPort \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialport.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialportglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QElapsedTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qelapsedtimer.h \
		../../api/dev_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QByteArray \
		../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		debug/moc_predefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/moc.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/soft/QT/Qt/5.15.2/mingw81_64/mkspecs/win32-g++ -IE:/work/code/QtCode/FiberComm -IE:/work/code/QtCode/FiberComm/api -IE:/work/code/QtCode/FiberComm/ui -IE:/work/code/QtCode/FiberComm/tool -IE:/work/code/QtCode/FiberComm/QtHiRedis_Lib_PATH/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtANGLE -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore -I. -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include ..\..\api\api_amplifier.h -o debug\moc_api_amplifier.cpp

debug/moc_api_opticalmodule.cpp: ../../api/api_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QElapsedTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qelapsedtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QCache \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../../api/qttelnet.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSize \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QRegExp \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/QTcpSocket \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtcpsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetworkglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetwork-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qabstractsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		../../api/dev_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QStringList \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMap \
		debug/moc_predefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/moc.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/soft/QT/Qt/5.15.2/mingw81_64/mkspecs/win32-g++ -IE:/work/code/QtCode/FiberComm -IE:/work/code/QtCode/FiberComm/api -IE:/work/code/QtCode/FiberComm/ui -IE:/work/code/QtCode/FiberComm/tool -IE:/work/code/QtCode/FiberComm/QtHiRedis_Lib_PATH/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtANGLE -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore -I. -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include ..\..\api\api_opticalmodule.h -o debug\moc_api_opticalmodule.cpp

debug/moc_qttelnet.cpp: ../../api/qttelnet.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSize \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QRegExp \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/QTcpSocket \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtcpsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetworkglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetwork-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qabstractsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		debug/moc_predefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/moc.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/soft/QT/Qt/5.15.2/mingw81_64/mkspecs/win32-g++ -IE:/work/code/QtCode/FiberComm -IE:/work/code/QtCode/FiberComm/api -IE:/work/code/QtCode/FiberComm/ui -IE:/work/code/QtCode/FiberComm/tool -IE:/work/code/QtCode/FiberComm/QtHiRedis_Lib_PATH/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtANGLE -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore -I. -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include ..\..\api\qttelnet.h -o debug\moc_qttelnet.cpp

debug/moc_mainwindow.cpp: ../../mainwindow.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QMainWindow \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qmainwindow.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrect.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qregion.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qline.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qimage.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfont.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtabwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qicon.h \
		debug/moc_predefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/moc.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/soft/QT/Qt/5.15.2/mingw81_64/mkspecs/win32-g++ -IE:/work/code/QtCode/FiberComm -IE:/work/code/QtCode/FiberComm/api -IE:/work/code/QtCode/FiberComm/ui -IE:/work/code/QtCode/FiberComm/tool -IE:/work/code/QtCode/FiberComm/QtHiRedis_Lib_PATH/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtANGLE -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore -I. -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include ..\..\mainwindow.h -o debug\moc_mainwindow.cpp

debug/moc_formamplifier.cpp: ../../ui/formamplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrect.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qregion.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qline.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qimage.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfont.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QLabel \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qlabel.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		../../api/api_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/QSerialPort \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialport.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialportglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QElapsedTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qelapsedtimer.h \
		../../api/dev_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QByteArray \
		../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		debug/moc_predefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/moc.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/soft/QT/Qt/5.15.2/mingw81_64/mkspecs/win32-g++ -IE:/work/code/QtCode/FiberComm -IE:/work/code/QtCode/FiberComm/api -IE:/work/code/QtCode/FiberComm/ui -IE:/work/code/QtCode/FiberComm/tool -IE:/work/code/QtCode/FiberComm/QtHiRedis_Lib_PATH/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtANGLE -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore -I. -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include ..\..\ui\formamplifier.h -o debug\moc_formamplifier.cpp

debug/moc_formcombineddev.cpp: ../../ui/formcombineddev.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrect.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qregion.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qline.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qimage.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfont.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimerEvent \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QLabel \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qlabel.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/QCloseEvent \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDateTime \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatetime.h \
		../../api/api_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QElapsedTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qelapsedtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QCache \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../../api/qttelnet.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSize \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QRegExp \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/QTcpSocket \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtcpsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetworkglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetwork-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qabstractsocket.h \
		../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		../../api/dev_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QStringList \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMap \
		../../api/api_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/QSerialPort \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialport.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialportglobal.h \
		../../api/dev_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QByteArray \
		../../ui/formamplifier.h \
		../../tool/global.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QMessageBox \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qmessagebox.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qdialog.h \
		debug/moc_predefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/moc.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/soft/QT/Qt/5.15.2/mingw81_64/mkspecs/win32-g++ -IE:/work/code/QtCode/FiberComm -IE:/work/code/QtCode/FiberComm/api -IE:/work/code/QtCode/FiberComm/ui -IE:/work/code/QtCode/FiberComm/tool -IE:/work/code/QtCode/FiberComm/QtHiRedis_Lib_PATH/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtANGLE -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore -I. -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include ..\..\ui\formcombineddev.h -o debug\moc_formcombineddev.cpp

debug/moc_formopticalmodule.cpp: ../../ui/formopticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrect.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qregion.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qline.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qimage.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfont.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QLabel \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qlabel.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		../../api/api_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QElapsedTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qelapsedtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QCache \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../../api/qttelnet.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSize \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QRegExp \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/QTcpSocket \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtcpsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetworkglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetwork-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qabstractsocket.h \
		../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		../../api/dev_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QStringList \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMap \
		debug/moc_predefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/moc.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/soft/QT/Qt/5.15.2/mingw81_64/mkspecs/win32-g++ -IE:/work/code/QtCode/FiberComm -IE:/work/code/QtCode/FiberComm/api -IE:/work/code/QtCode/FiberComm/ui -IE:/work/code/QtCode/FiberComm/tool -IE:/work/code/QtCode/FiberComm/QtHiRedis_Lib_PATH/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtANGLE -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore -I. -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include ..\..\ui\formopticalmodule.h -o debug\moc_formopticalmodule.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all: debug/qttelnet.moc
compiler_moc_source_clean:
	-$(DEL_FILE) debug\qttelnet.moc
debug/qttelnet.moc: ../../api/qttelnet.cpp \
		../../api/qttelnet.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSize \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QRegExp \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/QTcpSocket \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtcpsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetworkglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetwork-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qabstractsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QList \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMap \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QPair \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QVariant \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSocketNotifier \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsocketnotifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QBuffer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbuffer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QVarLengthArray \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDebug \
		debug/moc_predefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/moc.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include E:/work/code/QtCode/FiberComm/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/soft/QT/Qt/5.15.2/mingw81_64/mkspecs/win32-g++ -IE:/work/code/QtCode/FiberComm -IE:/work/code/QtCode/FiberComm/api -IE:/work/code/QtCode/FiberComm/ui -IE:/work/code/QtCode/FiberComm/tool -IE:/work/code/QtCode/FiberComm/QtHiRedis_Lib_PATH/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtANGLE -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork -ID:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore -I. -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/soft/QT/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/soft/QT/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include ..\..\api\qttelnet.cpp -o debug\qttelnet.moc

compiler_uic_make_all: ui_mainwindow.h ui_formamplifier.h ui_formcombineddev.h ui_formopticalmodule.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h ui_formamplifier.h ui_formcombineddev.h ui_formopticalmodule.h
ui_mainwindow.h: ../../mainwindow.ui \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/uic.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\uic.exe ..\..\mainwindow.ui -o ui_mainwindow.h

ui_formamplifier.h: ../../ui/formamplifier.ui \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/uic.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\uic.exe ..\..\ui\formamplifier.ui -o ui_formamplifier.h

ui_formcombineddev.h: ../../ui/formcombineddev.ui \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/uic.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\uic.exe ..\..\ui\formcombineddev.ui -o ui_formcombineddev.h

ui_formopticalmodule.h: ../../ui/formopticalmodule.ui \
		D:/soft/QT/Qt/5.15.2/mingw81_64/bin/uic.exe
	D:\soft\QT\Qt\5.15.2\mingw81_64\bin\uic.exe ..\..\ui\formopticalmodule.ui -o ui_formopticalmodule.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_moc_source_clean compiler_uic_clean 



####### Compile

debug/api_amplifier.o: ../../api/api_amplifier.cpp ../../api/api_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/QSerialPort \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialport.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialportglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QElapsedTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qelapsedtimer.h \
		../../api/dev_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QByteArray \
		../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		../../tool/global.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QMessageBox \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qmessagebox.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qdialog.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrect.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qregion.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qline.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qimage.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfont.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDebug \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasictimer.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\api_amplifier.o ..\..\api\api_amplifier.cpp

debug/api_opticalmodule.o: ../../api/api_opticalmodule.cpp ../../api/api_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QElapsedTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qelapsedtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QCache \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../../api/qttelnet.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSize \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QRegExp \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/QTcpSocket \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtcpsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetworkglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetwork-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qabstractsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		../../api/dev_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QStringList \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMap \
		../../tool/global.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QMessageBox \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qmessagebox.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qdialog.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrect.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qregion.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qline.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qimage.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfont.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTime \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatetime.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtMath \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmath.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QCoreApplication \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreapplication.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qeventloop.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QEventLoop
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\api_opticalmodule.o ..\..\api\api_opticalmodule.cpp

debug/api_redisclient.o: ../../api/api_redisclient.cpp ../../api/api_redisclient.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		../../QtHiRedis_Lib_PATH/include/adapters/qt.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSocketNotifier \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsocketnotifier.h \
		../../QtHiRedis_Lib_PATH/include/async.h \
		../../QtHiRedis_Lib_PATH/include/hiredis.h \
		../../QtHiRedis_Lib_PATH/include/read.h \
		../../QtHiRedis_Lib_PATH/include/sds.h \
		../../QtHiRedis_Lib_PATH/include/alloc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutexLocker \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDebug \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\api_redisclient.o ..\..\api\api_redisclient.cpp

debug/qttelnet.o: ../../api/qttelnet.cpp ../../api/qttelnet.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSize \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QRegExp \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/QTcpSocket \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtcpsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetworkglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetwork-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qabstractsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QList \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMap \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QPair \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QVariant \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSocketNotifier \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsocketnotifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QBuffer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbuffer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QVarLengthArray \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDebug \
		debug/qttelnet.moc
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\qttelnet.o ..\..\api\qttelnet.cpp

debug/main.o: ../../main.cpp ../../mainwindow.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QMainWindow \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qmainwindow.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrect.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qregion.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qline.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qimage.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfont.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtabwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qicon.h \
		../../ui/formopticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QLabel \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qlabel.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		../../api/api_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QElapsedTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qelapsedtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QCache \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../../api/qttelnet.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSize \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QRegExp \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/QTcpSocket \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtcpsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetworkglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetwork-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qabstractsocket.h \
		../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		../../api/dev_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QStringList \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMap \
		../../ui/formcombineddev.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimerEvent \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/QCloseEvent \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDateTime \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatetime.h \
		../../api/api_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/QSerialPort \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialport.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialportglobal.h \
		../../api/dev_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QByteArray \
		../../ui/formamplifier.h \
		../../tool/global.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QMessageBox \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qmessagebox.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qdialog.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QApplication \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qapplication.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreapplication.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qeventloop.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qdesktopwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qguiapplication.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qinputmethod.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\main.o ..\..\main.cpp

debug/mainwindow.o: ../../mainwindow.cpp ../../mainwindow.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QMainWindow \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qmainwindow.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrect.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qregion.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qline.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qimage.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfont.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtabwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qicon.h \
		ui_mainwindow.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\mainwindow.o ..\..\mainwindow.cpp

debug/logs.o: ../../tool/logs.cpp ../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutexLocker \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDate \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatetime.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTime \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\logs.o ..\..\tool\logs.cpp

debug/netconfigsettings.o: ../../tool/netconfigsettings.cpp ../../tool/netconfigsettings.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QVariantMap \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSettings \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsettings.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDebug \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QCoreApplication \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreapplication.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qeventloop.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\netconfigsettings.o ..\..\tool\netconfigsettings.cpp

debug/formamplifier.o: ../../ui/formamplifier.cpp ../../ui/formamplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrect.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qregion.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qline.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qimage.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfont.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QLabel \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qlabel.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		../../api/api_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/QSerialPort \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialport.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialportglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QElapsedTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qelapsedtimer.h \
		../../api/dev_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QByteArray \
		../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		ui_formamplifier.h \
		../../tool/netconfigsettings.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QVariantMap \
		../../tool/global.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QMessageBox \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qmessagebox.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qdialog.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QBitArray \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbitarray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\formamplifier.o ..\..\ui\formamplifier.cpp

debug/formcombineddev.o: ../../ui/formcombineddev.cpp ../../ui/formcombineddev.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrect.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qregion.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qline.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qimage.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfont.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimerEvent \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QLabel \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qlabel.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/QCloseEvent \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDateTime \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatetime.h \
		../../api/api_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QElapsedTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qelapsedtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QCache \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../../api/qttelnet.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSize \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QRegExp \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/QTcpSocket \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtcpsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetworkglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetwork-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qabstractsocket.h \
		../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		../../api/dev_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QStringList \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMap \
		../../api/api_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/QSerialPort \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialport.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtSerialPort/qserialportglobal.h \
		../../api/dev_amplifier.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QByteArray \
		../../ui/formamplifier.h \
		../../tool/global.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QMessageBox \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qmessagebox.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qdialog.h \
		ui_formcombineddev.h \
		../../tool/netconfigsettings.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QVariantMap \
		../../api/api_redisclient.h \
		../../QtHiRedis_Lib_PATH/include/adapters/qt.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSocketNotifier \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsocketnotifier.h \
		../../QtHiRedis_Lib_PATH/include/async.h \
		../../QtHiRedis_Lib_PATH/include/hiredis.h \
		../../QtHiRedis_Lib_PATH/include/read.h \
		../../QtHiRedis_Lib_PATH/include/sds.h \
		../../QtHiRedis_Lib_PATH/include/alloc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutexLocker \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtMath \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmath.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QStringLiteral \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QCompleter \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qcompleter.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qabstractitemmodel.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QBitArray \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbitarray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/QDesktopServices \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qdesktopservices.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstandardpaths.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QDesktopWidget \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qdesktopwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QApplication \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qapplication.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreapplication.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qeventloop.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qguiapplication.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qinputmethod.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QList
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\formcombineddev.o ..\..\ui\formcombineddev.cpp

debug/formopticalmodule.o: ../../ui/formopticalmodule.cpp ../../ui/formopticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qflags.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstring.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qchar.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpair.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvector.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qrect.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsize.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qregion.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qline.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qimage.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qhash.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfont.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qevent.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmap.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qset.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurl.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfile.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QFile \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/QLabel \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qlabel.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		../../api/api_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QObject \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QString \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QElapsedTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qelapsedtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QCache \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qcache.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMutex \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTimer \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../../api/qttelnet.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QSize \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QRegExp \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/QTcpSocket \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtcpsocket.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetworkglobal.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qtnetwork-config.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtNetwork/qabstractsocket.h \
		../../tool/logs.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDir \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdir.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QTextStream \
		../../api/dev_opticalmodule.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QStringList \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QMap \
		ui_formopticalmodule.h \
		../../tool/netconfigsettings.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QVariantMap \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QByteArray \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QDateTime \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qdatetime.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/QDesktopServices \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtGui/qdesktopservices.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qstandardpaths.h \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QUrl \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/QtMath \
		D:/soft/QT/Qt/5.15.2/mingw81_64/include/QtCore/qmath.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\formopticalmodule.o ..\..\ui\formopticalmodule.cpp

debug/qrc_resource.o: debug/qrc_resource.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\qrc_resource.o debug\qrc_resource.cpp

debug/moc_qt.o: debug/moc_qt.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_qt.o debug\moc_qt.cpp

debug/moc_api_redisclient.o: debug/moc_api_redisclient.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_api_redisclient.o debug\moc_api_redisclient.cpp

debug/moc_api_amplifier.o: debug/moc_api_amplifier.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_api_amplifier.o debug\moc_api_amplifier.cpp

debug/moc_api_opticalmodule.o: debug/moc_api_opticalmodule.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_api_opticalmodule.o debug\moc_api_opticalmodule.cpp

debug/moc_qttelnet.o: debug/moc_qttelnet.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_qttelnet.o debug\moc_qttelnet.cpp

debug/moc_mainwindow.o: debug/moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_mainwindow.o debug\moc_mainwindow.cpp

debug/moc_formamplifier.o: debug/moc_formamplifier.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_formamplifier.o debug\moc_formamplifier.cpp

debug/moc_formcombineddev.o: debug/moc_formcombineddev.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_formcombineddev.o debug\moc_formcombineddev.cpp

debug/moc_formopticalmodule.o: debug/moc_formopticalmodule.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_formopticalmodule.o debug\moc_formopticalmodule.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

