/****************************************************************************
** Meta object code from reading C++ file 'formcombineddev.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../ui/formcombineddev.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'formcombineddev.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_FormCombinedDev_t {
    QByteArrayData data[44];
    char stringdata0[925];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_FormCombinedDev_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_FormCombinedDev_t qt_meta_stringdata_FormCombinedDev = {
    {
QT_MOC_LITERAL(0, 0, 15), // "FormCombinedDev"
QT_MOC_LITERAL(1, 16, 7), // "refData"
QT_MOC_LITERAL(2, 24, 0), // ""
QT_MOC_LITERAL(3, 25, 19), // "on_btnSet_1_clicked"
QT_MOC_LITERAL(4, 45, 19), // "on_btnSet_2_clicked"
QT_MOC_LITERAL(5, 65, 19), // "on_btnSet_3_clicked"
QT_MOC_LITERAL(6, 85, 19), // "on_btnSet_4_clicked"
QT_MOC_LITERAL(7, 105, 19), // "on_btnSet_6_clicked"
QT_MOC_LITERAL(8, 125, 19), // "on_btnSet_7_clicked"
QT_MOC_LITERAL(9, 145, 19), // "on_btnSet_8_clicked"
QT_MOC_LITERAL(10, 165, 20), // "on_btnSet_10_clicked"
QT_MOC_LITERAL(11, 186, 20), // "on_btnSet_11_clicked"
QT_MOC_LITERAL(12, 207, 20), // "on_btnSet_12_clicked"
QT_MOC_LITERAL(13, 228, 20), // "on_btnSet_14_clicked"
QT_MOC_LITERAL(14, 249, 20), // "on_btnSet_15_clicked"
QT_MOC_LITERAL(15, 270, 23), // "on_btnAmplifier_clicked"
QT_MOC_LITERAL(16, 294, 24), // "on_btnRecordData_clicked"
QT_MOC_LITERAL(17, 319, 22), // "on_btnOpenData_clicked"
QT_MOC_LITERAL(18, 342, 41), // "on_ctrl_channel_num_1_current..."
QT_MOC_LITERAL(19, 384, 5), // "index"
QT_MOC_LITERAL(20, 390, 41), // "on_ctrl_channel_num_2_current..."
QT_MOC_LITERAL(21, 432, 20), // "on_btnSet_13_clicked"
QT_MOC_LITERAL(22, 453, 20), // "on_btnSet_18_clicked"
QT_MOC_LITERAL(23, 474, 20), // "on_btnSet_16_clicked"
QT_MOC_LITERAL(24, 495, 20), // "on_btnSet_17_clicked"
QT_MOC_LITERAL(25, 516, 20), // "on_btnSet_19_clicked"
QT_MOC_LITERAL(26, 537, 20), // "on_btnSet_20_clicked"
QT_MOC_LITERAL(27, 558, 20), // "on_btnSet_21_clicked"
QT_MOC_LITERAL(28, 579, 20), // "on_btnSet_22_clicked"
QT_MOC_LITERAL(29, 600, 20), // "on_btnSet_25_clicked"
QT_MOC_LITERAL(30, 621, 20), // "on_btnSet_23_clicked"
QT_MOC_LITERAL(31, 642, 20), // "on_btnSet_24_clicked"
QT_MOC_LITERAL(32, 663, 33), // "on_ctrl_module_status_1_activ..."
QT_MOC_LITERAL(33, 697, 4), // "arg1"
QT_MOC_LITERAL(34, 702, 19), // "on_btnSet_9_clicked"
QT_MOC_LITERAL(35, 722, 20), // "on_btnSet_27_clicked"
QT_MOC_LITERAL(36, 743, 20), // "on_btnSet_26_clicked"
QT_MOC_LITERAL(37, 764, 20), // "on_btnSet_28_clicked"
QT_MOC_LITERAL(38, 785, 30), // "on_sta_scanstart_linkActivated"
QT_MOC_LITERAL(39, 816, 4), // "link"
QT_MOC_LITERAL(40, 821, 19), // "on_btnSet_5_clicked"
QT_MOC_LITERAL(41, 841, 40), // "on_ctrl_start_freq_cursorPosi..."
QT_MOC_LITERAL(42, 882, 4), // "arg2"
QT_MOC_LITERAL(43, 887, 37) // "on_comboBox_light_currentInde..."

    },
    "FormCombinedDev\0refData\0\0on_btnSet_1_clicked\0"
    "on_btnSet_2_clicked\0on_btnSet_3_clicked\0"
    "on_btnSet_4_clicked\0on_btnSet_6_clicked\0"
    "on_btnSet_7_clicked\0on_btnSet_8_clicked\0"
    "on_btnSet_10_clicked\0on_btnSet_11_clicked\0"
    "on_btnSet_12_clicked\0on_btnSet_14_clicked\0"
    "on_btnSet_15_clicked\0on_btnAmplifier_clicked\0"
    "on_btnRecordData_clicked\0"
    "on_btnOpenData_clicked\0"
    "on_ctrl_channel_num_1_currentIndexChanged\0"
    "index\0on_ctrl_channel_num_2_currentIndexChanged\0"
    "on_btnSet_13_clicked\0on_btnSet_18_clicked\0"
    "on_btnSet_16_clicked\0on_btnSet_17_clicked\0"
    "on_btnSet_19_clicked\0on_btnSet_20_clicked\0"
    "on_btnSet_21_clicked\0on_btnSet_22_clicked\0"
    "on_btnSet_25_clicked\0on_btnSet_23_clicked\0"
    "on_btnSet_24_clicked\0"
    "on_ctrl_module_status_1_activated\0"
    "arg1\0on_btnSet_9_clicked\0on_btnSet_27_clicked\0"
    "on_btnSet_26_clicked\0on_btnSet_28_clicked\0"
    "on_sta_scanstart_linkActivated\0link\0"
    "on_btnSet_5_clicked\0"
    "on_ctrl_start_freq_cursorPositionChanged\0"
    "arg2\0on_comboBox_light_currentIndexChanged"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_FormCombinedDev[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      38,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  204,    2, 0x08 /* Private */,
       3,    0,  205,    2, 0x08 /* Private */,
       4,    0,  206,    2, 0x08 /* Private */,
       5,    0,  207,    2, 0x08 /* Private */,
       6,    0,  208,    2, 0x08 /* Private */,
       7,    0,  209,    2, 0x08 /* Private */,
       8,    0,  210,    2, 0x08 /* Private */,
       9,    0,  211,    2, 0x08 /* Private */,
      10,    0,  212,    2, 0x08 /* Private */,
      11,    0,  213,    2, 0x08 /* Private */,
      12,    0,  214,    2, 0x08 /* Private */,
      13,    0,  215,    2, 0x08 /* Private */,
      14,    0,  216,    2, 0x08 /* Private */,
      15,    0,  217,    2, 0x08 /* Private */,
      16,    0,  218,    2, 0x08 /* Private */,
      17,    0,  219,    2, 0x08 /* Private */,
      18,    1,  220,    2, 0x08 /* Private */,
      20,    1,  223,    2, 0x08 /* Private */,
      21,    0,  226,    2, 0x08 /* Private */,
      22,    0,  227,    2, 0x08 /* Private */,
      23,    0,  228,    2, 0x08 /* Private */,
      24,    0,  229,    2, 0x08 /* Private */,
      25,    0,  230,    2, 0x08 /* Private */,
      26,    0,  231,    2, 0x08 /* Private */,
      27,    0,  232,    2, 0x08 /* Private */,
      28,    0,  233,    2, 0x08 /* Private */,
      29,    0,  234,    2, 0x08 /* Private */,
      30,    0,  235,    2, 0x08 /* Private */,
      31,    0,  236,    2, 0x08 /* Private */,
      32,    1,  237,    2, 0x08 /* Private */,
      34,    0,  240,    2, 0x08 /* Private */,
      35,    0,  241,    2, 0x08 /* Private */,
      36,    0,  242,    2, 0x08 /* Private */,
      37,    0,  243,    2, 0x08 /* Private */,
      38,    1,  244,    2, 0x08 /* Private */,
      40,    0,  247,    2, 0x08 /* Private */,
      41,    2,  248,    2, 0x08 /* Private */,
      43,    1,  253,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   19,
    QMetaType::Void, QMetaType::Int,   19,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   33,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   39,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,   33,   42,
    QMetaType::Void, QMetaType::Int,   19,

       0        // eod
};

void FormCombinedDev::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<FormCombinedDev *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->refData(); break;
        case 1: _t->on_btnSet_1_clicked(); break;
        case 2: _t->on_btnSet_2_clicked(); break;
        case 3: _t->on_btnSet_3_clicked(); break;
        case 4: _t->on_btnSet_4_clicked(); break;
        case 5: _t->on_btnSet_6_clicked(); break;
        case 6: _t->on_btnSet_7_clicked(); break;
        case 7: _t->on_btnSet_8_clicked(); break;
        case 8: _t->on_btnSet_10_clicked(); break;
        case 9: _t->on_btnSet_11_clicked(); break;
        case 10: _t->on_btnSet_12_clicked(); break;
        case 11: _t->on_btnSet_14_clicked(); break;
        case 12: _t->on_btnSet_15_clicked(); break;
        case 13: _t->on_btnAmplifier_clicked(); break;
        case 14: _t->on_btnRecordData_clicked(); break;
        case 15: _t->on_btnOpenData_clicked(); break;
        case 16: _t->on_ctrl_channel_num_1_currentIndexChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 17: _t->on_ctrl_channel_num_2_currentIndexChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 18: _t->on_btnSet_13_clicked(); break;
        case 19: _t->on_btnSet_18_clicked(); break;
        case 20: _t->on_btnSet_16_clicked(); break;
        case 21: _t->on_btnSet_17_clicked(); break;
        case 22: _t->on_btnSet_19_clicked(); break;
        case 23: _t->on_btnSet_20_clicked(); break;
        case 24: _t->on_btnSet_21_clicked(); break;
        case 25: _t->on_btnSet_22_clicked(); break;
        case 26: _t->on_btnSet_25_clicked(); break;
        case 27: _t->on_btnSet_23_clicked(); break;
        case 28: _t->on_btnSet_24_clicked(); break;
        case 29: _t->on_ctrl_module_status_1_activated((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 30: _t->on_btnSet_9_clicked(); break;
        case 31: _t->on_btnSet_27_clicked(); break;
        case 32: _t->on_btnSet_26_clicked(); break;
        case 33: _t->on_btnSet_28_clicked(); break;
        case 34: _t->on_sta_scanstart_linkActivated((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 35: _t->on_btnSet_5_clicked(); break;
        case 36: _t->on_ctrl_start_freq_cursorPositionChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 37: _t->on_comboBox_light_currentIndexChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject FormCombinedDev::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_FormCombinedDev.data,
    qt_meta_data_FormCombinedDev,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *FormCombinedDev::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *FormCombinedDev::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_FormCombinedDev.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int FormCombinedDev::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 38)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 38;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 38)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 38;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
