/****************************************************************************
** Meta object code from reading C++ file 'formopticalmodule.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../ui/formopticalmodule.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'formopticalmodule.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_FormOpticalModule_t {
    QByteArrayData data[9];
    char stringdata0[181];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_FormOpticalModule_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_FormOpticalModule_t qt_meta_stringdata_FormOpticalModule = {
    {
QT_MOC_LITERAL(0, 0, 17), // "FormOpticalModule"
QT_MOC_LITERAL(1, 18, 25), // "on_btnTestCommand_clicked"
QT_MOC_LITERAL(2, 44, 0), // ""
QT_MOC_LITERAL(3, 45, 22), // "on_btnSet_b010_clicked"
QT_MOC_LITERAL(4, 68, 22), // "on_btnSet_b410_clicked"
QT_MOC_LITERAL(5, 91, 22), // "on_btnSet_b400_clicked"
QT_MOC_LITERAL(6, 114, 22), // "on_btnSet_b430_clicked"
QT_MOC_LITERAL(7, 137, 20), // "on_btnRecord_clicked"
QT_MOC_LITERAL(8, 158, 22) // "on_btnRecord_2_clicked"

    },
    "FormOpticalModule\0on_btnTestCommand_clicked\0"
    "\0on_btnSet_b010_clicked\0on_btnSet_b410_clicked\0"
    "on_btnSet_b400_clicked\0on_btnSet_b430_clicked\0"
    "on_btnRecord_clicked\0on_btnRecord_2_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_FormOpticalModule[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   49,    2, 0x08 /* Private */,
       3,    0,   50,    2, 0x08 /* Private */,
       4,    0,   51,    2, 0x08 /* Private */,
       5,    0,   52,    2, 0x08 /* Private */,
       6,    0,   53,    2, 0x08 /* Private */,
       7,    0,   54,    2, 0x08 /* Private */,
       8,    0,   55,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void FormOpticalModule::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<FormOpticalModule *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->on_btnTestCommand_clicked(); break;
        case 1: _t->on_btnSet_b010_clicked(); break;
        case 2: _t->on_btnSet_b410_clicked(); break;
        case 3: _t->on_btnSet_b400_clicked(); break;
        case 4: _t->on_btnSet_b430_clicked(); break;
        case 5: _t->on_btnRecord_clicked(); break;
        case 6: _t->on_btnRecord_2_clicked(); break;
        default: ;
        }
    }
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject FormOpticalModule::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_FormOpticalModule.data,
    qt_meta_data_FormOpticalModule,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *FormOpticalModule::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *FormOpticalModule::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_FormOpticalModule.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int FormOpticalModule::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
