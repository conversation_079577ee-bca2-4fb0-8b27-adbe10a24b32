/********************************************************************************
** Form generated from reading UI file 'formamplifier.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FORMAMPLIFIER_H
#define UI_FORMAMPLIFIER_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_FormAmplifier
{
public:
    QVBoxLayout *verticalLayout;
    QGroupBox *groupBox;
    QVBoxLayout *verticalLayout_2;
    QHBoxLayout *horizontalLayout;
    QLabel *label_11;
    QLabel *module_warn_high_temp;
    QSpacerItem *horizontalSpacer_5;
    QLabel *label_12;
    QLabel *module_warn_low_temp;
    QSpacerItem *horizontalSpacer_7;
    QLabel *label_14;
    QLabel *module_warn_input_power;
    QSpacerItem *horizontalSpacer_8;
    QLabel *label_13;
    QLabel *module_warn_output_power;
    QSpacerItem *horizontalSpacer_19;
    QHBoxLayout *horizontalLayout_15;
    QLabel *label;
    QLabel *module_temp;
    QSpacerItem *horizontalSpacer_34;
    QLabel *label_2;
    QLabel *module_input_vol;
    QSpacerItem *horizontalSpacer_35;
    QLabel *label_58;
    QLabel *online_status;
    QGroupBox *groupBox_2;
    QVBoxLayout *verticalLayout_3;
    QHBoxLayout *horizontalLayout_2;
    QLabel *label_15;
    QLabel *noise_warn_input_high_power;
    QSpacerItem *horizontalSpacer_10;
    QLabel *label_16;
    QLabel *noise_warn_input_low_power;
    QSpacerItem *horizontalSpacer_11;
    QLabel *label_18;
    QLabel *noise_warn_output_high_power;
    QSpacerItem *horizontalSpacer_13;
    QLabel *label_17;
    QLabel *noise_warn_output_low_power;
    QSpacerItem *horizontalSpacer_9;
    QGridLayout *gridLayout;
    QLabel *noise_output_power;
    QSpacerItem *horizontalSpacer_38;
    QLabel *label_5;
    QLabel *label_3;
    QLabel *noise_mode;
    QLabel *noise_input_power;
    QLabel *label_8;
    QSpacerItem *horizontalSpacer_37;
    QSpacerItem *horizontalSpacer_36;
    QLabel *noise_switch;
    QLabel *label_7;
    QGroupBox *groupBox_3;
    QVBoxLayout *verticalLayout_4;
    QHBoxLayout *horizontalLayout_3;
    QLabel *label_24;
    QLabel *power_warn_input_high_power;
    QSpacerItem *horizontalSpacer_14;
    QLabel *label_26;
    QLabel *power_warn_input_low_power;
    QSpacerItem *horizontalSpacer_16;
    QLabel *label_22;
    QLabel *power_warn_output_high_power;
    QSpacerItem *horizontalSpacer_17;
    QLabel *label_21;
    QLabel *power_warn_output_low_power;
    QSpacerItem *horizontalSpacer_15;
    QGridLayout *gridLayout_2;
    QSpacerItem *horizontalSpacer_39;
    QLabel *power_input_power;
    QLabel *power_mode;
    QLabel *power_switch;
    QLabel *label_19;
    QLabel *power_output_power;
    QLabel *label_25;
    QLabel *label_20;
    QLabel *label_23;
    QSpacerItem *horizontalSpacer_40;
    QSpacerItem *horizontalSpacer_41;
    QGroupBox *groupBox_4;
    QVBoxLayout *verticalLayout_5;
    QHBoxLayout *horizontalLayout_14;
    QLabel *label_34;
    QLabel *warn_current_high_1;
    QSpacerItem *horizontalSpacer_20;
    QLabel *label_32;
    QLabel *warn_current_low_1;
    QSpacerItem *horizontalSpacer_21;
    QLabel *label_31;
    QLabel *warn_temp_high_1;
    QSpacerItem *horizontalSpacer_22;
    QLabel *label_33;
    QLabel *warn_temp_low_1;
    QSpacerItem *horizontalSpacer_23;
    QLabel *label_37;
    QLabel *warn_backlight_high_1;
    QSpacerItem *horizontalSpacer_24;
    QLabel *label_38;
    QLabel *warn_backlight_low_1;
    QSpacerItem *horizontalSpacer_25;
    QLabel *label_36;
    QLabel *warn_tec_current_high_1;
    QSpacerItem *horizontalSpacer_26;
    QLabel *label_35;
    QLabel *warn_tec_current_low_1;
    QSpacerItem *horizontalSpacer;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label_27;
    QLabel *current_1;
    QSpacerItem *horizontalSpacer_42;
    QLabel *label_28;
    QLabel *temp_1;
    QSpacerItem *horizontalSpacer_43;
    QLabel *label_29;
    QLabel *backlight_current_1;
    QSpacerItem *horizontalSpacer_44;
    QLabel *label_30;
    QLabel *tec_current_1;
    QGroupBox *groupBox_5;
    QVBoxLayout *verticalLayout_6;
    QHBoxLayout *horizontalLayout_16;
    QLabel *label_39;
    QLabel *warn_current_high_2;
    QSpacerItem *horizontalSpacer_27;
    QLabel *label_40;
    QLabel *warn_current_low_2;
    QSpacerItem *horizontalSpacer_28;
    QLabel *label_41;
    QLabel *warn_temp_high_2;
    QSpacerItem *horizontalSpacer_29;
    QLabel *label_42;
    QLabel *warn_temp_low_2;
    QSpacerItem *horizontalSpacer_30;
    QLabel *label_43;
    QLabel *warn_backlight_high_2;
    QSpacerItem *horizontalSpacer_31;
    QLabel *label_44;
    QLabel *warn_backlight_low_2;
    QSpacerItem *horizontalSpacer_32;
    QLabel *label_45;
    QLabel *warn_tec_current_high_2;
    QSpacerItem *horizontalSpacer_33;
    QLabel *label_46;
    QLabel *warn_tec_current_low_2;
    QSpacerItem *horizontalSpacer_3;
    QHBoxLayout *horizontalLayout_5;
    QLabel *label_47;
    QLabel *current_2;
    QSpacerItem *horizontalSpacer_45;
    QLabel *label_48;
    QLabel *temp_2;
    QSpacerItem *horizontalSpacer_46;
    QLabel *label_49;
    QLabel *backlight_current_2;
    QSpacerItem *horizontalSpacer_47;
    QLabel *label_50;
    QLabel *tec_current_2;
    QGridLayout *gridLayout_12;
    QGroupBox *groupBox_6;
    QHBoxLayout *horizontalLayout_6;
    QGridLayout *gridLayout_6;
    QComboBox *channel_id_A001;
    QLabel *label_4;
    QLabel *label_6;
    QComboBox *switch_status_A001;
    QPushButton *btnA001;
    QGroupBox *groupBox_7;
    QHBoxLayout *horizontalLayout_7;
    QGridLayout *gridLayout_7;
    QLabel *label_10;
    QComboBox *channel_id_A002;
    QLabel *label_9;
    QComboBox *work_mode_A002;
    QPushButton *btnA002;
    QGroupBox *groupBox_8;
    QHBoxLayout *horizontalLayout_9;
    QGridLayout *gridLayout_9;
    QLineEdit *current_A003;
    QComboBox *pump_id_A003;
    QLabel *label_53;
    QLabel *label_54;
    QPushButton *btnA003;
    QGroupBox *groupBox_9;
    QHBoxLayout *horizontalLayout_10;
    QGridLayout *gridLayout_10;
    QComboBox *channel_id_A004;
    QLabel *label_55;
    QLineEdit *output_power_A004;
    QLabel *label_56;
    QPushButton *btnA004;
    QGroupBox *groupBox_10;
    QHBoxLayout *horizontalLayout_11;
    QGridLayout *gridLayout_11;
    QLabel *label_57;
    QComboBox *online_status_A007;
    QPushButton *btnA007;

    void setupUi(QWidget *FormAmplifier)
    {
        if (FormAmplifier->objectName().isEmpty())
            FormAmplifier->setObjectName(QString::fromUtf8("FormAmplifier"));
        FormAmplifier->resize(1121, 894);
        verticalLayout = new QVBoxLayout(FormAmplifier);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        groupBox = new QGroupBox(FormAmplifier);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        QFont font;
        font.setPointSize(16);
        groupBox->setFont(font);
        verticalLayout_2 = new QVBoxLayout(groupBox);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        label_11 = new QLabel(groupBox);
        label_11->setObjectName(QString::fromUtf8("label_11"));
        QFont font1;
        font1.setPointSize(14);
        label_11->setFont(font1);

        horizontalLayout->addWidget(label_11);

        module_warn_high_temp = new QLabel(groupBox);
        module_warn_high_temp->setObjectName(QString::fromUtf8("module_warn_high_temp"));
        module_warn_high_temp->setMinimumSize(QSize(32, 32));
        module_warn_high_temp->setMaximumSize(QSize(32, 32));
        module_warn_high_temp->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout->addWidget(module_warn_high_temp);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_5);

        label_12 = new QLabel(groupBox);
        label_12->setObjectName(QString::fromUtf8("label_12"));
        label_12->setFont(font1);

        horizontalLayout->addWidget(label_12);

        module_warn_low_temp = new QLabel(groupBox);
        module_warn_low_temp->setObjectName(QString::fromUtf8("module_warn_low_temp"));
        module_warn_low_temp->setMinimumSize(QSize(32, 32));
        module_warn_low_temp->setMaximumSize(QSize(32, 32));

        horizontalLayout->addWidget(module_warn_low_temp);

        horizontalSpacer_7 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_7);

        label_14 = new QLabel(groupBox);
        label_14->setObjectName(QString::fromUtf8("label_14"));
        label_14->setFont(font1);

        horizontalLayout->addWidget(label_14);

        module_warn_input_power = new QLabel(groupBox);
        module_warn_input_power->setObjectName(QString::fromUtf8("module_warn_input_power"));
        module_warn_input_power->setMinimumSize(QSize(32, 32));
        module_warn_input_power->setMaximumSize(QSize(32, 32));

        horizontalLayout->addWidget(module_warn_input_power);

        horizontalSpacer_8 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_8);

        label_13 = new QLabel(groupBox);
        label_13->setObjectName(QString::fromUtf8("label_13"));
        label_13->setFont(font1);

        horizontalLayout->addWidget(label_13);

        module_warn_output_power = new QLabel(groupBox);
        module_warn_output_power->setObjectName(QString::fromUtf8("module_warn_output_power"));
        module_warn_output_power->setMinimumSize(QSize(32, 32));
        module_warn_output_power->setMaximumSize(QSize(32, 32));

        horizontalLayout->addWidget(module_warn_output_power);

        horizontalSpacer_19 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_19);

        horizontalLayout->setStretch(0, 1);
        horizontalLayout->setStretch(1, 1);
        horizontalLayout->setStretch(3, 1);
        horizontalLayout->setStretch(4, 1);
        horizontalLayout->setStretch(6, 1);
        horizontalLayout->setStretch(7, 1);
        horizontalLayout->setStretch(9, 1);
        horizontalLayout->setStretch(10, 1);

        verticalLayout_2->addLayout(horizontalLayout);

        horizontalLayout_15 = new QHBoxLayout();
        horizontalLayout_15->setObjectName(QString::fromUtf8("horizontalLayout_15"));
        label = new QLabel(groupBox);
        label->setObjectName(QString::fromUtf8("label"));
        label->setFont(font1);

        horizontalLayout_15->addWidget(label);

        module_temp = new QLabel(groupBox);
        module_temp->setObjectName(QString::fromUtf8("module_temp"));
        module_temp->setMinimumSize(QSize(0, 36));
        module_temp->setFont(font1);
        module_temp->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        horizontalLayout_15->addWidget(module_temp);

        horizontalSpacer_34 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_15->addItem(horizontalSpacer_34);

        label_2 = new QLabel(groupBox);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setFont(font1);

        horizontalLayout_15->addWidget(label_2);

        module_input_vol = new QLabel(groupBox);
        module_input_vol->setObjectName(QString::fromUtf8("module_input_vol"));
        module_input_vol->setMinimumSize(QSize(0, 36));
        module_input_vol->setFont(font1);
        module_input_vol->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        horizontalLayout_15->addWidget(module_input_vol);

        horizontalSpacer_35 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_15->addItem(horizontalSpacer_35);

        label_58 = new QLabel(groupBox);
        label_58->setObjectName(QString::fromUtf8("label_58"));
        label_58->setFont(font1);

        horizontalLayout_15->addWidget(label_58);

        online_status = new QLabel(groupBox);
        online_status->setObjectName(QString::fromUtf8("online_status"));
        online_status->setMinimumSize(QSize(0, 36));
        online_status->setFont(font1);
        online_status->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        horizontalLayout_15->addWidget(online_status);

        horizontalLayout_15->setStretch(0, 1);
        horizontalLayout_15->setStretch(1, 1);
        horizontalLayout_15->setStretch(3, 1);
        horizontalLayout_15->setStretch(4, 1);
        horizontalLayout_15->setStretch(6, 1);
        horizontalLayout_15->setStretch(7, 1);

        verticalLayout_2->addLayout(horizontalLayout_15);


        verticalLayout->addWidget(groupBox);

        groupBox_2 = new QGroupBox(FormAmplifier);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        groupBox_2->setFont(font);
        verticalLayout_3 = new QVBoxLayout(groupBox_2);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        label_15 = new QLabel(groupBox_2);
        label_15->setObjectName(QString::fromUtf8("label_15"));
        label_15->setFont(font1);

        horizontalLayout_2->addWidget(label_15);

        noise_warn_input_high_power = new QLabel(groupBox_2);
        noise_warn_input_high_power->setObjectName(QString::fromUtf8("noise_warn_input_high_power"));
        noise_warn_input_high_power->setMinimumSize(QSize(32, 32));
        noise_warn_input_high_power->setMaximumSize(QSize(32, 32));
        noise_warn_input_high_power->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_2->addWidget(noise_warn_input_high_power);

        horizontalSpacer_10 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_10);

        label_16 = new QLabel(groupBox_2);
        label_16->setObjectName(QString::fromUtf8("label_16"));
        label_16->setFont(font1);

        horizontalLayout_2->addWidget(label_16);

        noise_warn_input_low_power = new QLabel(groupBox_2);
        noise_warn_input_low_power->setObjectName(QString::fromUtf8("noise_warn_input_low_power"));
        noise_warn_input_low_power->setMinimumSize(QSize(32, 32));
        noise_warn_input_low_power->setMaximumSize(QSize(32, 32));
        noise_warn_input_low_power->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_2->addWidget(noise_warn_input_low_power);

        horizontalSpacer_11 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_11);

        label_18 = new QLabel(groupBox_2);
        label_18->setObjectName(QString::fromUtf8("label_18"));
        label_18->setFont(font1);

        horizontalLayout_2->addWidget(label_18);

        noise_warn_output_high_power = new QLabel(groupBox_2);
        noise_warn_output_high_power->setObjectName(QString::fromUtf8("noise_warn_output_high_power"));
        noise_warn_output_high_power->setMinimumSize(QSize(32, 32));
        noise_warn_output_high_power->setMaximumSize(QSize(32, 32));
        noise_warn_output_high_power->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_2->addWidget(noise_warn_output_high_power);

        horizontalSpacer_13 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_13);

        label_17 = new QLabel(groupBox_2);
        label_17->setObjectName(QString::fromUtf8("label_17"));
        label_17->setFont(font);

        horizontalLayout_2->addWidget(label_17);

        noise_warn_output_low_power = new QLabel(groupBox_2);
        noise_warn_output_low_power->setObjectName(QString::fromUtf8("noise_warn_output_low_power"));
        noise_warn_output_low_power->setMinimumSize(QSize(32, 32));
        noise_warn_output_low_power->setMaximumSize(QSize(32, 32));
        noise_warn_output_low_power->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_2->addWidget(noise_warn_output_low_power);

        horizontalSpacer_9 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_9);

        horizontalLayout_2->setStretch(0, 1);
        horizontalLayout_2->setStretch(1, 1);
        horizontalLayout_2->setStretch(3, 1);
        horizontalLayout_2->setStretch(4, 1);
        horizontalLayout_2->setStretch(6, 1);
        horizontalLayout_2->setStretch(7, 1);
        horizontalLayout_2->setStretch(9, 1);
        horizontalLayout_2->setStretch(10, 1);

        verticalLayout_3->addLayout(horizontalLayout_2);

        gridLayout = new QGridLayout();
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        noise_output_power = new QLabel(groupBox_2);
        noise_output_power->setObjectName(QString::fromUtf8("noise_output_power"));
        noise_output_power->setFont(font);
        noise_output_power->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout->addWidget(noise_output_power, 0, 10, 1, 1);

        horizontalSpacer_38 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer_38, 0, 8, 1, 1);

        label_5 = new QLabel(groupBox_2);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setFont(font1);

        gridLayout->addWidget(label_5, 0, 3, 1, 1);

        label_3 = new QLabel(groupBox_2);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        label_3->setFont(font1);

        gridLayout->addWidget(label_3, 0, 0, 1, 1);

        noise_mode = new QLabel(groupBox_2);
        noise_mode->setObjectName(QString::fromUtf8("noise_mode"));
        noise_mode->setMinimumSize(QSize(0, 36));
        noise_mode->setFont(font1);
        noise_mode->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout->addWidget(noise_mode, 0, 1, 1, 1);

        noise_input_power = new QLabel(groupBox_2);
        noise_input_power->setObjectName(QString::fromUtf8("noise_input_power"));
        noise_input_power->setFont(font1);
        noise_input_power->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout->addWidget(noise_input_power, 0, 7, 1, 1);

        label_8 = new QLabel(groupBox_2);
        label_8->setObjectName(QString::fromUtf8("label_8"));
        label_8->setFont(font);

        gridLayout->addWidget(label_8, 0, 9, 1, 1);

        horizontalSpacer_37 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer_37, 0, 5, 1, 1);

        horizontalSpacer_36 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer_36, 0, 2, 1, 1);

        noise_switch = new QLabel(groupBox_2);
        noise_switch->setObjectName(QString::fromUtf8("noise_switch"));
        noise_switch->setFont(font1);
        noise_switch->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout->addWidget(noise_switch, 0, 4, 1, 1);

        label_7 = new QLabel(groupBox_2);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        label_7->setFont(font1);

        gridLayout->addWidget(label_7, 0, 6, 1, 1);

        gridLayout->setColumnStretch(0, 1);
        gridLayout->setColumnStretch(1, 1);
        gridLayout->setColumnStretch(3, 1);
        gridLayout->setColumnStretch(4, 1);
        gridLayout->setColumnStretch(6, 1);
        gridLayout->setColumnStretch(7, 1);
        gridLayout->setColumnStretch(9, 1);
        gridLayout->setColumnStretch(10, 1);

        verticalLayout_3->addLayout(gridLayout);


        verticalLayout->addWidget(groupBox_2);

        groupBox_3 = new QGroupBox(FormAmplifier);
        groupBox_3->setObjectName(QString::fromUtf8("groupBox_3"));
        groupBox_3->setFont(font);
        verticalLayout_4 = new QVBoxLayout(groupBox_3);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        label_24 = new QLabel(groupBox_3);
        label_24->setObjectName(QString::fromUtf8("label_24"));
        label_24->setFont(font1);

        horizontalLayout_3->addWidget(label_24);

        power_warn_input_high_power = new QLabel(groupBox_3);
        power_warn_input_high_power->setObjectName(QString::fromUtf8("power_warn_input_high_power"));
        power_warn_input_high_power->setMinimumSize(QSize(32, 32));
        power_warn_input_high_power->setMaximumSize(QSize(32, 32));
        power_warn_input_high_power->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_3->addWidget(power_warn_input_high_power);

        horizontalSpacer_14 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_14);

        label_26 = new QLabel(groupBox_3);
        label_26->setObjectName(QString::fromUtf8("label_26"));
        label_26->setFont(font1);

        horizontalLayout_3->addWidget(label_26);

        power_warn_input_low_power = new QLabel(groupBox_3);
        power_warn_input_low_power->setObjectName(QString::fromUtf8("power_warn_input_low_power"));
        power_warn_input_low_power->setMinimumSize(QSize(32, 32));
        power_warn_input_low_power->setMaximumSize(QSize(32, 32));
        power_warn_input_low_power->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_3->addWidget(power_warn_input_low_power);

        horizontalSpacer_16 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_16);

        label_22 = new QLabel(groupBox_3);
        label_22->setObjectName(QString::fromUtf8("label_22"));
        label_22->setFont(font1);

        horizontalLayout_3->addWidget(label_22);

        power_warn_output_high_power = new QLabel(groupBox_3);
        power_warn_output_high_power->setObjectName(QString::fromUtf8("power_warn_output_high_power"));
        power_warn_output_high_power->setMinimumSize(QSize(32, 32));
        power_warn_output_high_power->setMaximumSize(QSize(32, 32));
        power_warn_output_high_power->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_3->addWidget(power_warn_output_high_power);

        horizontalSpacer_17 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_17);

        label_21 = new QLabel(groupBox_3);
        label_21->setObjectName(QString::fromUtf8("label_21"));
        label_21->setFont(font);

        horizontalLayout_3->addWidget(label_21);

        power_warn_output_low_power = new QLabel(groupBox_3);
        power_warn_output_low_power->setObjectName(QString::fromUtf8("power_warn_output_low_power"));
        power_warn_output_low_power->setMinimumSize(QSize(32, 32));
        power_warn_output_low_power->setMaximumSize(QSize(32, 32));
        power_warn_output_low_power->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_3->addWidget(power_warn_output_low_power);

        horizontalSpacer_15 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_15);

        horizontalLayout_3->setStretch(0, 1);
        horizontalLayout_3->setStretch(1, 1);
        horizontalLayout_3->setStretch(3, 1);
        horizontalLayout_3->setStretch(4, 1);
        horizontalLayout_3->setStretch(6, 1);
        horizontalLayout_3->setStretch(7, 1);
        horizontalLayout_3->setStretch(9, 1);
        horizontalLayout_3->setStretch(10, 1);

        verticalLayout_4->addLayout(horizontalLayout_3);

        gridLayout_2 = new QGridLayout();
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        horizontalSpacer_39 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_2->addItem(horizontalSpacer_39, 0, 2, 1, 1);

        power_input_power = new QLabel(groupBox_3);
        power_input_power->setObjectName(QString::fromUtf8("power_input_power"));
        power_input_power->setFont(font1);
        power_input_power->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(power_input_power, 0, 7, 1, 1);

        power_mode = new QLabel(groupBox_3);
        power_mode->setObjectName(QString::fromUtf8("power_mode"));
        power_mode->setMinimumSize(QSize(0, 36));
        power_mode->setFont(font1);
        power_mode->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(power_mode, 0, 1, 1, 1);

        power_switch = new QLabel(groupBox_3);
        power_switch->setObjectName(QString::fromUtf8("power_switch"));
        power_switch->setFont(font1);
        power_switch->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(power_switch, 0, 4, 1, 1);

        label_19 = new QLabel(groupBox_3);
        label_19->setObjectName(QString::fromUtf8("label_19"));
        label_19->setFont(font);

        gridLayout_2->addWidget(label_19, 0, 9, 1, 1);

        power_output_power = new QLabel(groupBox_3);
        power_output_power->setObjectName(QString::fromUtf8("power_output_power"));
        power_output_power->setFont(font);
        power_output_power->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(power_output_power, 0, 10, 1, 1);

        label_25 = new QLabel(groupBox_3);
        label_25->setObjectName(QString::fromUtf8("label_25"));
        label_25->setFont(font1);

        gridLayout_2->addWidget(label_25, 0, 3, 1, 1);

        label_20 = new QLabel(groupBox_3);
        label_20->setObjectName(QString::fromUtf8("label_20"));
        label_20->setFont(font1);

        gridLayout_2->addWidget(label_20, 0, 6, 1, 1);

        label_23 = new QLabel(groupBox_3);
        label_23->setObjectName(QString::fromUtf8("label_23"));
        label_23->setFont(font1);

        gridLayout_2->addWidget(label_23, 0, 0, 1, 1);

        horizontalSpacer_40 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_2->addItem(horizontalSpacer_40, 0, 5, 1, 1);

        horizontalSpacer_41 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_2->addItem(horizontalSpacer_41, 0, 8, 1, 1);

        gridLayout_2->setColumnStretch(0, 1);
        gridLayout_2->setColumnStretch(1, 1);
        gridLayout_2->setColumnStretch(3, 1);
        gridLayout_2->setColumnStretch(4, 1);
        gridLayout_2->setColumnStretch(6, 1);
        gridLayout_2->setColumnStretch(7, 1);
        gridLayout_2->setColumnStretch(9, 1);
        gridLayout_2->setColumnStretch(10, 1);

        verticalLayout_4->addLayout(gridLayout_2);


        verticalLayout->addWidget(groupBox_3);

        groupBox_4 = new QGroupBox(FormAmplifier);
        groupBox_4->setObjectName(QString::fromUtf8("groupBox_4"));
        groupBox_4->setFont(font);
        verticalLayout_5 = new QVBoxLayout(groupBox_4);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        horizontalLayout_14 = new QHBoxLayout();
        horizontalLayout_14->setObjectName(QString::fromUtf8("horizontalLayout_14"));
        label_34 = new QLabel(groupBox_4);
        label_34->setObjectName(QString::fromUtf8("label_34"));
        label_34->setFont(font1);

        horizontalLayout_14->addWidget(label_34);

        warn_current_high_1 = new QLabel(groupBox_4);
        warn_current_high_1->setObjectName(QString::fromUtf8("warn_current_high_1"));
        warn_current_high_1->setMinimumSize(QSize(32, 32));
        warn_current_high_1->setMaximumSize(QSize(32, 32));
        warn_current_high_1->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_14->addWidget(warn_current_high_1);

        horizontalSpacer_20 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer_20);

        label_32 = new QLabel(groupBox_4);
        label_32->setObjectName(QString::fromUtf8("label_32"));
        label_32->setFont(font1);

        horizontalLayout_14->addWidget(label_32);

        warn_current_low_1 = new QLabel(groupBox_4);
        warn_current_low_1->setObjectName(QString::fromUtf8("warn_current_low_1"));
        warn_current_low_1->setMinimumSize(QSize(32, 32));
        warn_current_low_1->setMaximumSize(QSize(32, 32));
        warn_current_low_1->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_14->addWidget(warn_current_low_1);

        horizontalSpacer_21 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer_21);

        label_31 = new QLabel(groupBox_4);
        label_31->setObjectName(QString::fromUtf8("label_31"));
        label_31->setFont(font1);

        horizontalLayout_14->addWidget(label_31);

        warn_temp_high_1 = new QLabel(groupBox_4);
        warn_temp_high_1->setObjectName(QString::fromUtf8("warn_temp_high_1"));
        warn_temp_high_1->setMinimumSize(QSize(32, 32));
        warn_temp_high_1->setMaximumSize(QSize(32, 32));
        warn_temp_high_1->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_14->addWidget(warn_temp_high_1);

        horizontalSpacer_22 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer_22);

        label_33 = new QLabel(groupBox_4);
        label_33->setObjectName(QString::fromUtf8("label_33"));

        horizontalLayout_14->addWidget(label_33);

        warn_temp_low_1 = new QLabel(groupBox_4);
        warn_temp_low_1->setObjectName(QString::fromUtf8("warn_temp_low_1"));
        warn_temp_low_1->setMinimumSize(QSize(32, 32));
        warn_temp_low_1->setMaximumSize(QSize(32, 32));
        warn_temp_low_1->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_14->addWidget(warn_temp_low_1);

        horizontalSpacer_23 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer_23);

        label_37 = new QLabel(groupBox_4);
        label_37->setObjectName(QString::fromUtf8("label_37"));
        label_37->setFont(font1);

        horizontalLayout_14->addWidget(label_37);

        warn_backlight_high_1 = new QLabel(groupBox_4);
        warn_backlight_high_1->setObjectName(QString::fromUtf8("warn_backlight_high_1"));
        warn_backlight_high_1->setMinimumSize(QSize(32, 32));
        warn_backlight_high_1->setMaximumSize(QSize(32, 32));
        warn_backlight_high_1->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_14->addWidget(warn_backlight_high_1);

        horizontalSpacer_24 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer_24);

        label_38 = new QLabel(groupBox_4);
        label_38->setObjectName(QString::fromUtf8("label_38"));
        label_38->setFont(font1);

        horizontalLayout_14->addWidget(label_38);

        warn_backlight_low_1 = new QLabel(groupBox_4);
        warn_backlight_low_1->setObjectName(QString::fromUtf8("warn_backlight_low_1"));
        warn_backlight_low_1->setMinimumSize(QSize(32, 32));
        warn_backlight_low_1->setMaximumSize(QSize(32, 32));
        warn_backlight_low_1->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_14->addWidget(warn_backlight_low_1);

        horizontalSpacer_25 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer_25);

        label_36 = new QLabel(groupBox_4);
        label_36->setObjectName(QString::fromUtf8("label_36"));
        label_36->setFont(font1);

        horizontalLayout_14->addWidget(label_36);

        warn_tec_current_high_1 = new QLabel(groupBox_4);
        warn_tec_current_high_1->setObjectName(QString::fromUtf8("warn_tec_current_high_1"));
        warn_tec_current_high_1->setMinimumSize(QSize(32, 32));
        warn_tec_current_high_1->setMaximumSize(QSize(32, 32));
        warn_tec_current_high_1->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_14->addWidget(warn_tec_current_high_1);

        horizontalSpacer_26 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer_26);

        label_35 = new QLabel(groupBox_4);
        label_35->setObjectName(QString::fromUtf8("label_35"));

        horizontalLayout_14->addWidget(label_35);

        warn_tec_current_low_1 = new QLabel(groupBox_4);
        warn_tec_current_low_1->setObjectName(QString::fromUtf8("warn_tec_current_low_1"));
        warn_tec_current_low_1->setMinimumSize(QSize(32, 32));
        warn_tec_current_low_1->setMaximumSize(QSize(32, 32));
        warn_tec_current_low_1->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_14->addWidget(warn_tec_current_low_1);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer);

        horizontalLayout_14->setStretch(0, 1);
        horizontalLayout_14->setStretch(1, 1);
        horizontalLayout_14->setStretch(3, 1);
        horizontalLayout_14->setStretch(4, 1);
        horizontalLayout_14->setStretch(6, 1);
        horizontalLayout_14->setStretch(7, 1);
        horizontalLayout_14->setStretch(9, 1);
        horizontalLayout_14->setStretch(10, 1);
        horizontalLayout_14->setStretch(12, 1);
        horizontalLayout_14->setStretch(13, 1);
        horizontalLayout_14->setStretch(15, 1);
        horizontalLayout_14->setStretch(16, 1);
        horizontalLayout_14->setStretch(18, 1);
        horizontalLayout_14->setStretch(19, 1);
        horizontalLayout_14->setStretch(21, 1);
        horizontalLayout_14->setStretch(22, 1);

        verticalLayout_5->addLayout(horizontalLayout_14);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        label_27 = new QLabel(groupBox_4);
        label_27->setObjectName(QString::fromUtf8("label_27"));
        label_27->setFont(font1);

        horizontalLayout_4->addWidget(label_27);

        current_1 = new QLabel(groupBox_4);
        current_1->setObjectName(QString::fromUtf8("current_1"));
        current_1->setMinimumSize(QSize(0, 36));
        current_1->setFont(font1);
        current_1->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        horizontalLayout_4->addWidget(current_1);

        horizontalSpacer_42 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_42);

        label_28 = new QLabel(groupBox_4);
        label_28->setObjectName(QString::fromUtf8("label_28"));
        label_28->setFont(font1);

        horizontalLayout_4->addWidget(label_28);

        temp_1 = new QLabel(groupBox_4);
        temp_1->setObjectName(QString::fromUtf8("temp_1"));
        temp_1->setFont(font1);
        temp_1->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        horizontalLayout_4->addWidget(temp_1);

        horizontalSpacer_43 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_43);

        label_29 = new QLabel(groupBox_4);
        label_29->setObjectName(QString::fromUtf8("label_29"));
        label_29->setFont(font1);

        horizontalLayout_4->addWidget(label_29);

        backlight_current_1 = new QLabel(groupBox_4);
        backlight_current_1->setObjectName(QString::fromUtf8("backlight_current_1"));
        backlight_current_1->setFont(font1);
        backlight_current_1->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        horizontalLayout_4->addWidget(backlight_current_1);

        horizontalSpacer_44 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_44);

        label_30 = new QLabel(groupBox_4);
        label_30->setObjectName(QString::fromUtf8("label_30"));

        horizontalLayout_4->addWidget(label_30);

        tec_current_1 = new QLabel(groupBox_4);
        tec_current_1->setObjectName(QString::fromUtf8("tec_current_1"));
        tec_current_1->setFont(font);
        tec_current_1->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        horizontalLayout_4->addWidget(tec_current_1);

        horizontalLayout_4->setStretch(0, 1);
        horizontalLayout_4->setStretch(1, 1);
        horizontalLayout_4->setStretch(3, 1);
        horizontalLayout_4->setStretch(4, 1);
        horizontalLayout_4->setStretch(6, 1);
        horizontalLayout_4->setStretch(7, 1);
        horizontalLayout_4->setStretch(9, 1);
        horizontalLayout_4->setStretch(10, 1);

        verticalLayout_5->addLayout(horizontalLayout_4);


        verticalLayout->addWidget(groupBox_4);

        groupBox_5 = new QGroupBox(FormAmplifier);
        groupBox_5->setObjectName(QString::fromUtf8("groupBox_5"));
        groupBox_5->setFont(font);
        verticalLayout_6 = new QVBoxLayout(groupBox_5);
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        horizontalLayout_16 = new QHBoxLayout();
        horizontalLayout_16->setObjectName(QString::fromUtf8("horizontalLayout_16"));
        label_39 = new QLabel(groupBox_5);
        label_39->setObjectName(QString::fromUtf8("label_39"));
        label_39->setFont(font1);

        horizontalLayout_16->addWidget(label_39);

        warn_current_high_2 = new QLabel(groupBox_5);
        warn_current_high_2->setObjectName(QString::fromUtf8("warn_current_high_2"));
        warn_current_high_2->setMinimumSize(QSize(32, 32));
        warn_current_high_2->setMaximumSize(QSize(32, 32));
        warn_current_high_2->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_16->addWidget(warn_current_high_2);

        horizontalSpacer_27 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_27);

        label_40 = new QLabel(groupBox_5);
        label_40->setObjectName(QString::fromUtf8("label_40"));
        label_40->setFont(font1);

        horizontalLayout_16->addWidget(label_40);

        warn_current_low_2 = new QLabel(groupBox_5);
        warn_current_low_2->setObjectName(QString::fromUtf8("warn_current_low_2"));
        warn_current_low_2->setMinimumSize(QSize(32, 32));
        warn_current_low_2->setMaximumSize(QSize(32, 32));
        warn_current_low_2->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_16->addWidget(warn_current_low_2);

        horizontalSpacer_28 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_28);

        label_41 = new QLabel(groupBox_5);
        label_41->setObjectName(QString::fromUtf8("label_41"));
        label_41->setFont(font1);

        horizontalLayout_16->addWidget(label_41);

        warn_temp_high_2 = new QLabel(groupBox_5);
        warn_temp_high_2->setObjectName(QString::fromUtf8("warn_temp_high_2"));
        warn_temp_high_2->setMinimumSize(QSize(32, 32));
        warn_temp_high_2->setMaximumSize(QSize(32, 32));
        warn_temp_high_2->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_16->addWidget(warn_temp_high_2);

        horizontalSpacer_29 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_29);

        label_42 = new QLabel(groupBox_5);
        label_42->setObjectName(QString::fromUtf8("label_42"));

        horizontalLayout_16->addWidget(label_42);

        warn_temp_low_2 = new QLabel(groupBox_5);
        warn_temp_low_2->setObjectName(QString::fromUtf8("warn_temp_low_2"));
        warn_temp_low_2->setMinimumSize(QSize(32, 32));
        warn_temp_low_2->setMaximumSize(QSize(32, 32));
        warn_temp_low_2->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_16->addWidget(warn_temp_low_2);

        horizontalSpacer_30 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_30);

        label_43 = new QLabel(groupBox_5);
        label_43->setObjectName(QString::fromUtf8("label_43"));
        label_43->setFont(font1);

        horizontalLayout_16->addWidget(label_43);

        warn_backlight_high_2 = new QLabel(groupBox_5);
        warn_backlight_high_2->setObjectName(QString::fromUtf8("warn_backlight_high_2"));
        warn_backlight_high_2->setMinimumSize(QSize(32, 32));
        warn_backlight_high_2->setMaximumSize(QSize(32, 32));
        warn_backlight_high_2->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_16->addWidget(warn_backlight_high_2);

        horizontalSpacer_31 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_31);

        label_44 = new QLabel(groupBox_5);
        label_44->setObjectName(QString::fromUtf8("label_44"));
        label_44->setFont(font1);

        horizontalLayout_16->addWidget(label_44);

        warn_backlight_low_2 = new QLabel(groupBox_5);
        warn_backlight_low_2->setObjectName(QString::fromUtf8("warn_backlight_low_2"));
        warn_backlight_low_2->setMinimumSize(QSize(32, 32));
        warn_backlight_low_2->setMaximumSize(QSize(32, 32));
        warn_backlight_low_2->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_16->addWidget(warn_backlight_low_2);

        horizontalSpacer_32 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_32);

        label_45 = new QLabel(groupBox_5);
        label_45->setObjectName(QString::fromUtf8("label_45"));
        label_45->setFont(font1);

        horizontalLayout_16->addWidget(label_45);

        warn_tec_current_high_2 = new QLabel(groupBox_5);
        warn_tec_current_high_2->setObjectName(QString::fromUtf8("warn_tec_current_high_2"));
        warn_tec_current_high_2->setMinimumSize(QSize(32, 32));
        warn_tec_current_high_2->setMaximumSize(QSize(32, 32));
        warn_tec_current_high_2->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_16->addWidget(warn_tec_current_high_2);

        horizontalSpacer_33 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_33);

        label_46 = new QLabel(groupBox_5);
        label_46->setObjectName(QString::fromUtf8("label_46"));

        horizontalLayout_16->addWidget(label_46);

        warn_tec_current_low_2 = new QLabel(groupBox_5);
        warn_tec_current_low_2->setObjectName(QString::fromUtf8("warn_tec_current_low_2"));
        warn_tec_current_low_2->setMinimumSize(QSize(32, 32));
        warn_tec_current_low_2->setMaximumSize(QSize(32, 32));
        warn_tec_current_low_2->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_16->addWidget(warn_tec_current_low_2);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_3);

        horizontalLayout_16->setStretch(0, 1);
        horizontalLayout_16->setStretch(1, 1);
        horizontalLayout_16->setStretch(3, 1);
        horizontalLayout_16->setStretch(4, 1);
        horizontalLayout_16->setStretch(6, 1);
        horizontalLayout_16->setStretch(7, 1);
        horizontalLayout_16->setStretch(9, 1);
        horizontalLayout_16->setStretch(10, 1);
        horizontalLayout_16->setStretch(12, 1);
        horizontalLayout_16->setStretch(13, 1);
        horizontalLayout_16->setStretch(15, 1);
        horizontalLayout_16->setStretch(16, 1);
        horizontalLayout_16->setStretch(18, 1);
        horizontalLayout_16->setStretch(19, 1);
        horizontalLayout_16->setStretch(21, 1);
        horizontalLayout_16->setStretch(22, 1);

        verticalLayout_6->addLayout(horizontalLayout_16);

        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        label_47 = new QLabel(groupBox_5);
        label_47->setObjectName(QString::fromUtf8("label_47"));
        label_47->setFont(font1);

        horizontalLayout_5->addWidget(label_47);

        current_2 = new QLabel(groupBox_5);
        current_2->setObjectName(QString::fromUtf8("current_2"));
        current_2->setMinimumSize(QSize(0, 36));
        current_2->setFont(font1);
        current_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        horizontalLayout_5->addWidget(current_2);

        horizontalSpacer_45 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_45);

        label_48 = new QLabel(groupBox_5);
        label_48->setObjectName(QString::fromUtf8("label_48"));
        label_48->setFont(font1);

        horizontalLayout_5->addWidget(label_48);

        temp_2 = new QLabel(groupBox_5);
        temp_2->setObjectName(QString::fromUtf8("temp_2"));
        temp_2->setFont(font1);
        temp_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        horizontalLayout_5->addWidget(temp_2);

        horizontalSpacer_46 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_46);

        label_49 = new QLabel(groupBox_5);
        label_49->setObjectName(QString::fromUtf8("label_49"));
        label_49->setFont(font1);

        horizontalLayout_5->addWidget(label_49);

        backlight_current_2 = new QLabel(groupBox_5);
        backlight_current_2->setObjectName(QString::fromUtf8("backlight_current_2"));
        backlight_current_2->setFont(font1);
        backlight_current_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        horizontalLayout_5->addWidget(backlight_current_2);

        horizontalSpacer_47 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_47);

        label_50 = new QLabel(groupBox_5);
        label_50->setObjectName(QString::fromUtf8("label_50"));

        horizontalLayout_5->addWidget(label_50);

        tec_current_2 = new QLabel(groupBox_5);
        tec_current_2->setObjectName(QString::fromUtf8("tec_current_2"));
        tec_current_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        horizontalLayout_5->addWidget(tec_current_2);

        horizontalLayout_5->setStretch(0, 1);
        horizontalLayout_5->setStretch(1, 1);
        horizontalLayout_5->setStretch(3, 1);
        horizontalLayout_5->setStretch(4, 1);
        horizontalLayout_5->setStretch(6, 1);
        horizontalLayout_5->setStretch(7, 1);
        horizontalLayout_5->setStretch(9, 1);
        horizontalLayout_5->setStretch(10, 1);

        verticalLayout_6->addLayout(horizontalLayout_5);


        verticalLayout->addWidget(groupBox_5);

        gridLayout_12 = new QGridLayout();
        gridLayout_12->setObjectName(QString::fromUtf8("gridLayout_12"));
        groupBox_6 = new QGroupBox(FormAmplifier);
        groupBox_6->setObjectName(QString::fromUtf8("groupBox_6"));
        groupBox_6->setFont(font);
        horizontalLayout_6 = new QHBoxLayout(groupBox_6);
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        gridLayout_6 = new QGridLayout();
        gridLayout_6->setObjectName(QString::fromUtf8("gridLayout_6"));
        channel_id_A001 = new QComboBox(groupBox_6);
        channel_id_A001->setObjectName(QString::fromUtf8("channel_id_A001"));
        channel_id_A001->setMinimumSize(QSize(0, 36));
        channel_id_A001->setFont(font1);

        gridLayout_6->addWidget(channel_id_A001, 0, 1, 1, 1);

        label_4 = new QLabel(groupBox_6);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setFont(font1);

        gridLayout_6->addWidget(label_4, 0, 0, 1, 1);

        label_6 = new QLabel(groupBox_6);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        label_6->setFont(font1);

        gridLayout_6->addWidget(label_6, 0, 2, 1, 1);

        switch_status_A001 = new QComboBox(groupBox_6);
        switch_status_A001->setObjectName(QString::fromUtf8("switch_status_A001"));
        switch_status_A001->setMinimumSize(QSize(0, 36));
        switch_status_A001->setFont(font1);

        gridLayout_6->addWidget(switch_status_A001, 0, 3, 1, 1);

        btnA001 = new QPushButton(groupBox_6);
        btnA001->setObjectName(QString::fromUtf8("btnA001"));
        btnA001->setMinimumSize(QSize(0, 36));
        btnA001->setFont(font1);

        gridLayout_6->addWidget(btnA001, 0, 4, 1, 1);

        gridLayout_6->setColumnStretch(0, 1);
        gridLayout_6->setColumnStretch(1, 1);
        gridLayout_6->setColumnStretch(2, 1);
        gridLayout_6->setColumnStretch(3, 1);

        horizontalLayout_6->addLayout(gridLayout_6);


        gridLayout_12->addWidget(groupBox_6, 0, 0, 1, 1);

        groupBox_7 = new QGroupBox(FormAmplifier);
        groupBox_7->setObjectName(QString::fromUtf8("groupBox_7"));
        groupBox_7->setFont(font);
        horizontalLayout_7 = new QHBoxLayout(groupBox_7);
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        gridLayout_7 = new QGridLayout();
        gridLayout_7->setObjectName(QString::fromUtf8("gridLayout_7"));
        label_10 = new QLabel(groupBox_7);
        label_10->setObjectName(QString::fromUtf8("label_10"));

        gridLayout_7->addWidget(label_10, 0, 2, 1, 1);

        channel_id_A002 = new QComboBox(groupBox_7);
        channel_id_A002->setObjectName(QString::fromUtf8("channel_id_A002"));
        channel_id_A002->setMinimumSize(QSize(0, 36));
        channel_id_A002->setFont(font1);

        gridLayout_7->addWidget(channel_id_A002, 0, 1, 1, 1);

        label_9 = new QLabel(groupBox_7);
        label_9->setObjectName(QString::fromUtf8("label_9"));
        label_9->setFont(font1);

        gridLayout_7->addWidget(label_9, 0, 0, 1, 1);

        work_mode_A002 = new QComboBox(groupBox_7);
        work_mode_A002->setObjectName(QString::fromUtf8("work_mode_A002"));
        work_mode_A002->setMinimumSize(QSize(0, 36));
        work_mode_A002->setFont(font1);

        gridLayout_7->addWidget(work_mode_A002, 0, 3, 1, 1);

        btnA002 = new QPushButton(groupBox_7);
        btnA002->setObjectName(QString::fromUtf8("btnA002"));
        btnA002->setMinimumSize(QSize(0, 36));
        btnA002->setFont(font1);

        gridLayout_7->addWidget(btnA002, 0, 4, 1, 1);

        gridLayout_7->setColumnStretch(0, 1);
        gridLayout_7->setColumnStretch(1, 1);
        gridLayout_7->setColumnStretch(2, 1);
        gridLayout_7->setColumnStretch(3, 1);

        horizontalLayout_7->addLayout(gridLayout_7);


        gridLayout_12->addWidget(groupBox_7, 0, 1, 1, 1);

        groupBox_8 = new QGroupBox(FormAmplifier);
        groupBox_8->setObjectName(QString::fromUtf8("groupBox_8"));
        groupBox_8->setFont(font);
        horizontalLayout_9 = new QHBoxLayout(groupBox_8);
        horizontalLayout_9->setObjectName(QString::fromUtf8("horizontalLayout_9"));
        gridLayout_9 = new QGridLayout();
        gridLayout_9->setObjectName(QString::fromUtf8("gridLayout_9"));
        current_A003 = new QLineEdit(groupBox_8);
        current_A003->setObjectName(QString::fromUtf8("current_A003"));
        current_A003->setMinimumSize(QSize(0, 36));
        current_A003->setFont(font1);

        gridLayout_9->addWidget(current_A003, 0, 3, 1, 1);

        pump_id_A003 = new QComboBox(groupBox_8);
        pump_id_A003->setObjectName(QString::fromUtf8("pump_id_A003"));
        pump_id_A003->setMinimumSize(QSize(0, 36));
        pump_id_A003->setFont(font1);

        gridLayout_9->addWidget(pump_id_A003, 0, 1, 1, 1);

        label_53 = new QLabel(groupBox_8);
        label_53->setObjectName(QString::fromUtf8("label_53"));
        label_53->setFont(font1);

        gridLayout_9->addWidget(label_53, 0, 0, 1, 1);

        label_54 = new QLabel(groupBox_8);
        label_54->setObjectName(QString::fromUtf8("label_54"));
        label_54->setFont(font1);

        gridLayout_9->addWidget(label_54, 0, 2, 1, 1);

        btnA003 = new QPushButton(groupBox_8);
        btnA003->setObjectName(QString::fromUtf8("btnA003"));
        btnA003->setMinimumSize(QSize(0, 36));
        btnA003->setFont(font1);

        gridLayout_9->addWidget(btnA003, 0, 4, 1, 1);

        gridLayout_9->setColumnStretch(0, 1);
        gridLayout_9->setColumnStretch(1, 1);
        gridLayout_9->setColumnStretch(2, 1);
        gridLayout_9->setColumnStretch(3, 1);

        horizontalLayout_9->addLayout(gridLayout_9);


        gridLayout_12->addWidget(groupBox_8, 1, 0, 1, 1);

        groupBox_9 = new QGroupBox(FormAmplifier);
        groupBox_9->setObjectName(QString::fromUtf8("groupBox_9"));
        groupBox_9->setFont(font);
        horizontalLayout_10 = new QHBoxLayout(groupBox_9);
        horizontalLayout_10->setObjectName(QString::fromUtf8("horizontalLayout_10"));
        gridLayout_10 = new QGridLayout();
        gridLayout_10->setObjectName(QString::fromUtf8("gridLayout_10"));
        channel_id_A004 = new QComboBox(groupBox_9);
        channel_id_A004->setObjectName(QString::fromUtf8("channel_id_A004"));
        channel_id_A004->setMinimumSize(QSize(0, 36));
        channel_id_A004->setFont(font1);

        gridLayout_10->addWidget(channel_id_A004, 0, 1, 1, 1);

        label_55 = new QLabel(groupBox_9);
        label_55->setObjectName(QString::fromUtf8("label_55"));
        label_55->setFont(font1);

        gridLayout_10->addWidget(label_55, 0, 0, 1, 1);

        output_power_A004 = new QLineEdit(groupBox_9);
        output_power_A004->setObjectName(QString::fromUtf8("output_power_A004"));
        output_power_A004->setMinimumSize(QSize(0, 36));
        output_power_A004->setFont(font1);
        output_power_A004->setStyleSheet(QString::fromUtf8("color: rgb(0, 85, 0);"));

        gridLayout_10->addWidget(output_power_A004, 0, 3, 1, 1);

        label_56 = new QLabel(groupBox_9);
        label_56->setObjectName(QString::fromUtf8("label_56"));

        gridLayout_10->addWidget(label_56, 0, 2, 1, 1);

        btnA004 = new QPushButton(groupBox_9);
        btnA004->setObjectName(QString::fromUtf8("btnA004"));
        btnA004->setMinimumSize(QSize(0, 36));
        btnA004->setFont(font1);

        gridLayout_10->addWidget(btnA004, 0, 4, 1, 1);

        gridLayout_10->setColumnStretch(0, 1);
        gridLayout_10->setColumnStretch(1, 1);
        gridLayout_10->setColumnStretch(2, 1);
        gridLayout_10->setColumnStretch(3, 1);

        horizontalLayout_10->addLayout(gridLayout_10);


        gridLayout_12->addWidget(groupBox_9, 1, 1, 1, 1);

        groupBox_10 = new QGroupBox(FormAmplifier);
        groupBox_10->setObjectName(QString::fromUtf8("groupBox_10"));
        groupBox_10->setFont(font);
        horizontalLayout_11 = new QHBoxLayout(groupBox_10);
        horizontalLayout_11->setObjectName(QString::fromUtf8("horizontalLayout_11"));
        gridLayout_11 = new QGridLayout();
        gridLayout_11->setObjectName(QString::fromUtf8("gridLayout_11"));
        label_57 = new QLabel(groupBox_10);
        label_57->setObjectName(QString::fromUtf8("label_57"));
        label_57->setFont(font1);

        gridLayout_11->addWidget(label_57, 0, 0, 1, 1);

        online_status_A007 = new QComboBox(groupBox_10);
        online_status_A007->setObjectName(QString::fromUtf8("online_status_A007"));
        online_status_A007->setMinimumSize(QSize(0, 36));
        online_status_A007->setFont(font1);

        gridLayout_11->addWidget(online_status_A007, 0, 1, 1, 1);

        btnA007 = new QPushButton(groupBox_10);
        btnA007->setObjectName(QString::fromUtf8("btnA007"));
        btnA007->setMinimumSize(QSize(0, 36));
        btnA007->setFont(font1);

        gridLayout_11->addWidget(btnA007, 0, 2, 1, 1);

        gridLayout_11->setColumnStretch(0, 1);
        gridLayout_11->setColumnStretch(1, 1);
        gridLayout_11->setColumnStretch(2, 1);

        horizontalLayout_11->addLayout(gridLayout_11);


        gridLayout_12->addWidget(groupBox_10, 2, 0, 1, 1);


        verticalLayout->addLayout(gridLayout_12);


        retranslateUi(FormAmplifier);

        QMetaObject::connectSlotsByName(FormAmplifier);
    } // setupUi

    void retranslateUi(QWidget *FormAmplifier)
    {
        FormAmplifier->setWindowTitle(QCoreApplication::translate("FormAmplifier", "Form", nullptr));
        groupBox->setTitle(QCoreApplication::translate("FormAmplifier", "\346\250\241\345\235\227\347\212\266\346\200\201", nullptr));
        label_11->setText(QCoreApplication::translate("FormAmplifier", "\346\270\251\345\272\246\351\253\230\357\274\232", nullptr));
        module_warn_high_temp->setText(QString());
        label_12->setText(QCoreApplication::translate("FormAmplifier", "\346\270\251\345\272\246\344\275\216\357\274\232", nullptr));
        module_warn_low_temp->setText(QString());
        label_14->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\205\245\347\224\265\345\216\213\357\274\232", nullptr));
        module_warn_input_power->setText(QString());
        label_13->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\207\272\347\224\265\345\216\213\357\274\232", nullptr));
        module_warn_output_power->setText(QString());
        label->setText(QCoreApplication::translate("FormAmplifier", "\346\270\251\345\272\246(\342\204\203)\357\274\232", nullptr));
        module_temp->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_2->setText(QCoreApplication::translate("FormAmplifier", "\346\250\241\345\235\227\350\276\223\345\205\245\347\224\265\345\216\213(mV)\357\274\232", nullptr));
        module_input_vol->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_58->setText(QCoreApplication::translate("FormAmplifier", "\344\270\212\347\224\265\347\212\266\346\200\201\357\274\232", nullptr));
        online_status->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        groupBox_2->setTitle(QCoreApplication::translate("FormAmplifier", "\344\275\216\345\231\252\345\243\260\347\212\266\346\200\201", nullptr));
        label_15->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\205\245\345\212\237\347\216\207\351\253\230\357\274\232", nullptr));
        noise_warn_input_high_power->setText(QString());
        label_16->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\205\245\345\212\237\347\216\207\344\275\216\357\274\232", nullptr));
        noise_warn_input_low_power->setText(QString());
        label_18->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\207\272\345\212\237\347\216\207\351\253\230\357\274\232", nullptr));
        noise_warn_output_high_power->setText(QString());
        label_17->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\207\272\345\212\237\347\216\207\344\275\216\357\274\232", nullptr));
        noise_warn_output_low_power->setText(QString());
        noise_output_power->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_5->setText(QCoreApplication::translate("FormAmplifier", "\345\274\200\345\205\263\357\274\232", nullptr));
        label_3->setText(QCoreApplication::translate("FormAmplifier", "\346\250\241\345\274\217\357\274\232", nullptr));
        noise_mode->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        noise_input_power->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_8->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\207\272\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        noise_switch->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_7->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\205\245\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        groupBox_3->setTitle(QCoreApplication::translate("FormAmplifier", "\351\253\230\345\212\237\347\216\207\347\212\266\346\200\201", nullptr));
        label_24->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\205\245\345\212\237\347\216\207\351\253\230\357\274\232", nullptr));
        power_warn_input_high_power->setText(QString());
        label_26->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\205\245\345\212\237\347\216\207\344\275\216\357\274\232", nullptr));
        power_warn_input_low_power->setText(QString());
        label_22->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\207\272\345\212\237\347\216\207\351\253\230\357\274\232", nullptr));
        power_warn_output_high_power->setText(QString());
        label_21->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\207\272\345\212\237\347\216\207\344\275\216\357\274\232", nullptr));
        power_warn_output_low_power->setText(QString());
        power_input_power->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        power_mode->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        power_switch->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_19->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\207\272\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        power_output_power->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_25->setText(QCoreApplication::translate("FormAmplifier", "\345\274\200\345\205\263\357\274\232", nullptr));
        label_20->setText(QCoreApplication::translate("FormAmplifier", "\350\276\223\345\205\245\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        label_23->setText(QCoreApplication::translate("FormAmplifier", "\346\250\241\345\274\217\357\274\232", nullptr));
        groupBox_4->setTitle(QCoreApplication::translate("FormAmplifier", "\346\263\265\346\265\2461\347\212\266\346\200\201", nullptr));
        label_34->setText(QCoreApplication::translate("FormAmplifier", "\347\224\265\346\265\201\351\253\230\357\274\232", nullptr));
        warn_current_high_1->setText(QString());
        label_32->setText(QCoreApplication::translate("FormAmplifier", "\347\224\265\346\265\201\344\275\216\357\274\232", nullptr));
        warn_current_low_1->setText(QString());
        label_31->setText(QCoreApplication::translate("FormAmplifier", "\346\270\251\345\272\246\351\253\230\357\274\232", nullptr));
        warn_temp_high_1->setText(QString());
        label_33->setText(QCoreApplication::translate("FormAmplifier", "\346\270\251\345\272\246\344\275\216\357\274\232", nullptr));
        warn_temp_low_1->setText(QString());
        label_37->setText(QCoreApplication::translate("FormAmplifier", "\350\203\214\345\205\211\351\253\230\357\274\232", nullptr));
        warn_backlight_high_1->setText(QString());
        label_38->setText(QCoreApplication::translate("FormAmplifier", "\350\203\214\345\205\211\344\275\216\357\274\232", nullptr));
        warn_backlight_low_1->setText(QString());
        label_36->setText(QCoreApplication::translate("FormAmplifier", "TEC\347\224\265\346\265\201\351\253\230\357\274\232", nullptr));
        warn_tec_current_high_1->setText(QString());
        label_35->setText(QCoreApplication::translate("FormAmplifier", "TEC\347\224\265\346\265\201\344\275\216\357\274\232", nullptr));
        warn_tec_current_low_1->setText(QString());
        label_27->setText(QCoreApplication::translate("FormAmplifier", "\347\224\265\346\265\201(mA)\357\274\232", nullptr));
        current_1->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_28->setText(QCoreApplication::translate("FormAmplifier", "\346\270\251\345\272\246(\342\204\203)\357\274\232", nullptr));
        temp_1->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_29->setText(QCoreApplication::translate("FormAmplifier", "\350\203\214\345\205\211\347\224\265\346\265\201(mA)\357\274\232", nullptr));
        backlight_current_1->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_30->setText(QCoreApplication::translate("FormAmplifier", "TEC\347\224\265\346\265\201(mA)\357\274\232", nullptr));
        tec_current_1->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        groupBox_5->setTitle(QCoreApplication::translate("FormAmplifier", "\346\263\265\346\265\2462\347\212\266\346\200\201", nullptr));
        label_39->setText(QCoreApplication::translate("FormAmplifier", "\347\224\265\346\265\201\351\253\230\357\274\232", nullptr));
        warn_current_high_2->setText(QString());
        label_40->setText(QCoreApplication::translate("FormAmplifier", "\347\224\265\346\265\201\344\275\216\357\274\232", nullptr));
        warn_current_low_2->setText(QString());
        label_41->setText(QCoreApplication::translate("FormAmplifier", "\346\270\251\345\272\246\351\253\230\357\274\232", nullptr));
        warn_temp_high_2->setText(QString());
        label_42->setText(QCoreApplication::translate("FormAmplifier", "\346\270\251\345\272\246\344\275\216\357\274\232", nullptr));
        warn_temp_low_2->setText(QString());
        label_43->setText(QCoreApplication::translate("FormAmplifier", "\350\203\214\345\205\211\351\253\230\357\274\232", nullptr));
        warn_backlight_high_2->setText(QString());
        label_44->setText(QCoreApplication::translate("FormAmplifier", "\350\203\214\345\205\211\344\275\216\357\274\232", nullptr));
        warn_backlight_low_2->setText(QString());
        label_45->setText(QCoreApplication::translate("FormAmplifier", "TEC\347\224\265\346\265\201\351\253\230\357\274\232", nullptr));
        warn_tec_current_high_2->setText(QString());
        label_46->setText(QCoreApplication::translate("FormAmplifier", "TEC\347\224\265\346\265\201\344\275\216\357\274\232", nullptr));
        warn_tec_current_low_2->setText(QString());
        label_47->setText(QCoreApplication::translate("FormAmplifier", "\347\224\265\346\265\201(mA)\357\274\232", nullptr));
        current_2->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_48->setText(QCoreApplication::translate("FormAmplifier", "\346\270\251\345\272\246(\342\204\203)\357\274\232", nullptr));
        temp_2->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_49->setText(QCoreApplication::translate("FormAmplifier", "\350\203\214\345\205\211\347\224\265\346\265\201(mA)\357\274\232", nullptr));
        backlight_current_2->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        label_50->setText(QCoreApplication::translate("FormAmplifier", "TEC\347\224\265\346\265\201(mA)\357\274\232", nullptr));
        tec_current_2->setText(QCoreApplication::translate("FormAmplifier", "TextLabel", nullptr));
        groupBox_6->setTitle(QCoreApplication::translate("FormAmplifier", "\350\256\276\347\275\256\351\200\232\351\201\223\345\274\200\345\205\263\347\212\266\346\200\201", nullptr));
        label_4->setText(QCoreApplication::translate("FormAmplifier", "\351\200\232\351\201\223\345\217\267\357\274\232", nullptr));
        label_6->setText(QCoreApplication::translate("FormAmplifier", "\345\274\200\345\205\263\347\212\266\346\200\201\357\274\232", nullptr));
        btnA001->setText(QCoreApplication::translate("FormAmplifier", "\350\256\276\347\275\256", nullptr));
        groupBox_7->setTitle(QCoreApplication::translate("FormAmplifier", "\350\256\276\347\275\256\351\200\232\351\201\223\345\267\245\344\275\234\346\250\241\345\274\217", nullptr));
        label_10->setText(QCoreApplication::translate("FormAmplifier", "\345\267\245\344\275\234\346\250\241\345\274\217\357\274\232", nullptr));
        label_9->setText(QCoreApplication::translate("FormAmplifier", "\351\200\232\351\201\223\345\217\267\357\274\232", nullptr));
        btnA002->setText(QCoreApplication::translate("FormAmplifier", "\350\256\276\347\275\256", nullptr));
        groupBox_8->setTitle(QCoreApplication::translate("FormAmplifier", "\350\256\276\347\275\256\346\263\265\346\265\246\347\224\265\346\265\201\345\200\274", nullptr));
        current_A003->setText(QCoreApplication::translate("FormAmplifier", "0", nullptr));
        label_53->setText(QCoreApplication::translate("FormAmplifier", "\346\263\265\346\265\246\347\274\226\345\217\267\357\274\232", nullptr));
        label_54->setText(QCoreApplication::translate("FormAmplifier", "\347\224\265\346\265\201\345\200\274(mA)\357\274\232", nullptr));
        btnA003->setText(QCoreApplication::translate("FormAmplifier", "\350\256\276\347\275\256", nullptr));
        groupBox_9->setTitle(QCoreApplication::translate("FormAmplifier", "\350\256\276\347\275\256\351\200\232\351\201\223\350\276\223\345\207\272\345\212\237\347\216\207\345\200\274", nullptr));
        label_55->setText(QCoreApplication::translate("FormAmplifier", "\351\200\232\351\201\223\345\217\267\357\274\232", nullptr));
        output_power_A004->setText(QCoreApplication::translate("FormAmplifier", "-60", nullptr));
        label_56->setText(QCoreApplication::translate("FormAmplifier", "\345\212\237\347\216\207\345\200\274(dBm)\357\274\232", nullptr));
        btnA004->setText(QCoreApplication::translate("FormAmplifier", "\350\256\276\347\275\256", nullptr));
        groupBox_10->setTitle(QCoreApplication::translate("FormAmplifier", "\350\256\276\347\275\256\344\270\212\347\224\265\347\212\266\346\200\201", nullptr));
        label_57->setText(QCoreApplication::translate("FormAmplifier", "\344\270\212\347\224\265\347\212\266\346\200\201\357\274\232", nullptr));
        btnA007->setText(QCoreApplication::translate("FormAmplifier", "\350\256\276\347\275\256", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FormAmplifier: public Ui_FormAmplifier {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FORMAMPLIFIER_H
