/********************************************************************************
** Form generated from reading UI file 'formcombineddev.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FORMCOMBINEDDEV_H
#define UI_FORMCOMBINEDDEV_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_FormCombinedDev
{
public:
    QHBoxLayout *horizontalLayout_41;
    QVBoxLayout *verticalLayout_3;
    QGroupBox *groupBox;
    QVBoxLayout *verticalLayout;
    QGroupBox *groupBox_14;
    QHBoxLayout *horizontalLayout_11;
    QGridLayout *gridLayout;
    QSpacerItem *horizontalSpacer_6;
    QLabel *sta_amplifier_status_1;
    QLabel *sta_trans_1;
    QSpacerItem *horizontalSpacer;
    QLabel *label_5;
    QSpacerItem *horizontalSpacer_2;
    QLabel *sta_reset_1;
    QLabel *label_2;
    QLabel *label_21;
    QLabel *label_19;
    QLabel *sta_ready_1;
    QGridLayout *gridLayout_2;
    QLabel *label_3;
    QLabel *module_temp_1;
    QLabel *label_28;
    QLabel *label_10;
    QLabel *hpa_input_power_1;
    QLabel *carrier_power_1;
    QLabel *label_16;
    QLabel *output_power_1;
    QLabel *label_4;
    QLabel *hpa_output_power_1;
    QLabel *label_41;
    QLabel *wave_len_1;
    QGroupBox *groupBox_3;
    QHBoxLayout *horizontalLayout_12;
    QHBoxLayout *horizontalLayout;
    QLabel *label_11;
    QComboBox *ctrl_module_status_1;
    QPushButton *btnSet_1;
    QGroupBox *groupBox_20;
    QHBoxLayout *horizontalLayout_33;
    QLabel *label_38;
    QLabel *sta_mod_status_1;
    QPushButton *btnSet_11;
    QPushButton *btnSet_19;
    QGroupBox *groupBox_4;
    QHBoxLayout *horizontalLayout_13;
    QHBoxLayout *horizontalLayout_2;
    QLabel *label_12;
    QLineEdit *ctrl_output_power_1;
    QPushButton *btnSet_2;
    QGroupBox *groupBox_16;
    QHBoxLayout *horizontalLayout_25;
    QHBoxLayout *horizontalLayout_26;
    QLabel *label_34;
    QLineEdit *sta_hpa_workmode_1;
    QComboBox *ctrl_hpa_workmode_1;
    QPushButton *btnSet_13;
    QGroupBox *groupBox_5;
    QHBoxLayout *horizontalLayout_16;
    QHBoxLayout *horizontalLayout_3;
    QLabel *label_13;
    QLineEdit *ctrl_hpa_output_power_1;
    QPushButton *btnSet_3;
    QGroupBox *groupBox_18;
    QHBoxLayout *horizontalLayout_27;
    QHBoxLayout *horizontalLayout_28;
    QLabel *label_35;
    QLineEdit *sta_current_1;
    QLineEdit *ctrl_current_1;
    QPushButton *btnSet_16;
    QGroupBox *groupBox_6;
    QHBoxLayout *horizontalLayout_17;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label_14;
    QLineEdit *wave_length_1;
    QComboBox *ctrl_channel_num_1;
    QPushButton *btnSet_4;
    QGroupBox *groupBox_7;
    QHBoxLayout *horizontalLayout_18;
    QHBoxLayout *horizontalLayout_5;
    QLabel *label_23;
    QLineEdit *ctrl_freq_compensate_1;
    QPushButton *btnSet_23;
    QGroupBox *groupBox_21;
    QHBoxLayout *horizontalLayout_34;
    QHBoxLayout *horizontalLayout_35;
    QLabel *label_39;
    QLineEdit *ctrl_wave_compensate_1;
    QPushButton *btnSet_20;
    QGroupBox *groupBox_23;
    QHBoxLayout *horizontalLayout_38;
    QHBoxLayout *horizontalLayout_39;
    QLabel *label_43;
    QLineEdit *ctrl_freq_1;
    QPushButton *btnSet_22;
    QGroupBox *groupBox_25;
    QGridLayout *gridLayout_5;
    QLineEdit *ctrl_start_bin;
    QPushButton *btnSet_26;
    QLabel *sta_raser_on;
    QPushButton *btnSet_27;
    QLabel *sta_scanstart;
    QLabel *sta_raser_off;
    QPushButton *btnSet_28;
    QLabel *label_47;
    QGroupBox *groupBox_26;
    QVBoxLayout *verticalLayout_5;
    QGridLayout *gridLayout_6;
    QLineEdit *ctrl_start_freq;
    QLineEdit *ctrl_end_freq;
    QLabel *label_45;
    QLabel *label_44;
    QLabel *label_48;
    QLineEdit *ctrl_scan_rate;
    QPushButton *btnSet_5;
    QGroupBox *groupBox_12;
    QHBoxLayout *horizontalLayout_14;
    QLabel *sta_open_prbs;
    QPushButton *btnSet_10;
    QPushButton *btnSet_12;
    QHBoxLayout *horizontalLayout_10;
    QLabel *label;
    QComboBox *comboBox_light;
    QSpacerItem *horizontalSpacer_5;
    QPushButton *btnAmplifier;
    QPushButton *btnRecordData;
    QPushButton *btnOpenData;
    QVBoxLayout *verticalLayout_6;
    QGroupBox *groupBox_2;
    QVBoxLayout *verticalLayout_2;
    QGroupBox *groupBox_15;
    QHBoxLayout *horizontalLayout_23;
    QGridLayout *gridLayout_3;
    QLabel *label_22;
    QLabel *sta_ready_2;
    QSpacerItem *horizontalSpacer_3;
    QLabel *label_20;
    QSpacerItem *horizontalSpacer_4;
    QLabel *label_6;
    QSpacerItem *horizontalSpacer_7;
    QLabel *sta_reset_2;
    QLabel *sta_trans_2;
    QLabel *label_7;
    QLabel *sta_amplifier_status_2;
    QGridLayout *gridLayout_4;
    QLabel *bit_count_2;
    QLabel *label_18;
    QLabel *evm_2;
    QLabel *freq_offset_2;
    QLabel *label_25;
    QLabel *hpa_input_power_2;
    QLabel *label_17;
    QLabel *label_15;
    QLabel *module_temp_2;
    QLabel *carrier_power_2;
    QLabel *label_8;
    QLabel *label_31;
    QLabel *label_32;
    QLabel *output_power_2;
    QLabel *label_9;
    QLabel *hpa_output_power_2;
    QLabel *label_42;
    QLabel *wave_len_2;
    QLabel *label_24;
    QLabel *snr_2;
    QLabel *label_33;
    QLabel *error_code_rate_2;
    QVBoxLayout *verticalLayout_4;
    QGroupBox *groupBox_8;
    QHBoxLayout *horizontalLayout_19;
    QHBoxLayout *horizontalLayout_6;
    QLabel *label_26;
    QComboBox *ctrl_module_status_2;
    QPushButton *btnSet_6;
    QGroupBox *groupBox_17;
    QHBoxLayout *horizontalLayout_30;
    QHBoxLayout *horizontalLayout_31;
    QLabel *label_37;
    QLineEdit *sta_hpa_workmode_2;
    QComboBox *ctrl_hpa_workmode_2;
    QPushButton *btnSet_18;
    QGroupBox *groupBox_9;
    QHBoxLayout *horizontalLayout_20;
    QHBoxLayout *horizontalLayout_7;
    QLabel *label_27;
    QLineEdit *ctrl_hpa_output_power_2;
    QPushButton *btnSet_7;
    QGroupBox *groupBox_19;
    QHBoxLayout *horizontalLayout_29;
    QHBoxLayout *horizontalLayout_32;
    QLabel *label_36;
    QLineEdit *sta_current_2;
    QLineEdit *ctrl_current_2;
    QPushButton *btnSet_17;
    QGroupBox *groupBox_10;
    QHBoxLayout *horizontalLayout_21;
    QHBoxLayout *horizontalLayout_8;
    QLabel *label_29;
    QLineEdit *wave_length_2;
    QComboBox *ctrl_channel_num_2;
    QPushButton *btnSet_8;
    QGroupBox *groupBox_11;
    QHBoxLayout *horizontalLayout_22;
    QHBoxLayout *horizontalLayout_9;
    QLabel *label_30;
    QLineEdit *ctrl_freq_compensate_2;
    QPushButton *btnSet_9;
    QGroupBox *groupBox_22;
    QHBoxLayout *horizontalLayout_36;
    QHBoxLayout *horizontalLayout_37;
    QLabel *label_40;
    QLineEdit *ctrl_wave_compensate_2;
    QPushButton *btnSet_21;
    QGroupBox *groupBox_24;
    QHBoxLayout *horizontalLayout_44;
    QHBoxLayout *horizontalLayout_45;
    QLabel *label_46;
    QLineEdit *ctrl_freq_2;
    QPushButton *btnSet_25;
    QGroupBox *groupBox_13;
    QHBoxLayout *horizontalLayout_15;
    QLabel *sta_recv_prbs;
    QPushButton *btnSet_14;
    QPushButton *btnSet_15;

    void setupUi(QWidget *FormCombinedDev)
    {
        if (FormCombinedDev->objectName().isEmpty())
            FormCombinedDev->setObjectName(QString::fromUtf8("FormCombinedDev"));
        FormCombinedDev->resize(1562, 1222);
        horizontalLayout_41 = new QHBoxLayout(FormCombinedDev);
        horizontalLayout_41->setObjectName(QString::fromUtf8("horizontalLayout_41"));
        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        groupBox = new QGroupBox(FormCombinedDev);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        QFont font;
        font.setPointSize(14);
        font.setBold(true);
        font.setWeight(75);
        groupBox->setFont(font);
        groupBox->setStyleSheet(QString::fromUtf8("QGroupBox#groupBox::title{\n"
"	font-weight:bold; \n"
"	font-size:30px; \n"
"}"));
        groupBox->setTitle(QString::fromUtf8("\345\217\221\351\200\201\347\253\257"));
        verticalLayout = new QVBoxLayout(groupBox);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(-1, 19, -1, -1);
        groupBox_14 = new QGroupBox(groupBox);
        groupBox_14->setObjectName(QString::fromUtf8("groupBox_14"));
        groupBox_14->setMaximumSize(QSize(16777215, 70));
        horizontalLayout_11 = new QHBoxLayout(groupBox_14);
        horizontalLayout_11->setObjectName(QString::fromUtf8("horizontalLayout_11"));
        gridLayout = new QGridLayout();
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer_6, 0, 8, 1, 1);

        sta_amplifier_status_1 = new QLabel(groupBox_14);
        sta_amplifier_status_1->setObjectName(QString::fromUtf8("sta_amplifier_status_1"));
        sta_amplifier_status_1->setMinimumSize(QSize(32, 32));
        sta_amplifier_status_1->setMaximumSize(QSize(32, 32));
        QFont font1;
        font1.setPointSize(14);
        font1.setBold(false);
        font1.setWeight(50);
        sta_amplifier_status_1->setFont(font1);
        sta_amplifier_status_1->setStyleSheet(QString::fromUtf8(""));
        sta_amplifier_status_1->setScaledContents(true);

        gridLayout->addWidget(sta_amplifier_status_1, 0, 10, 1, 1);

        sta_trans_1 = new QLabel(groupBox_14);
        sta_trans_1->setObjectName(QString::fromUtf8("sta_trans_1"));
        sta_trans_1->setFont(font1);
        sta_trans_1->setStyleSheet(QString::fromUtf8(""));
        sta_trans_1->setScaledContents(true);

        gridLayout->addWidget(sta_trans_1, 0, 7, 1, 1);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer, 0, 2, 1, 1);

        label_5 = new QLabel(groupBox_14);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setFont(font1);

        gridLayout->addWidget(label_5, 0, 6, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer_2, 0, 5, 1, 1);

        sta_reset_1 = new QLabel(groupBox_14);
        sta_reset_1->setObjectName(QString::fromUtf8("sta_reset_1"));
        sta_reset_1->setFont(font1);
        sta_reset_1->setStyleSheet(QString::fromUtf8(""));
        sta_reset_1->setScaledContents(true);

        gridLayout->addWidget(sta_reset_1, 0, 1, 1, 1);

        label_2 = new QLabel(groupBox_14);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setFont(font1);

        gridLayout->addWidget(label_2, 0, 3, 1, 1);

        label_21 = new QLabel(groupBox_14);
        label_21->setObjectName(QString::fromUtf8("label_21"));
        label_21->setFont(font1);

        gridLayout->addWidget(label_21, 0, 9, 1, 1);

        label_19 = new QLabel(groupBox_14);
        label_19->setObjectName(QString::fromUtf8("label_19"));
        label_19->setFont(font1);

        gridLayout->addWidget(label_19, 0, 0, 1, 1);

        sta_ready_1 = new QLabel(groupBox_14);
        sta_ready_1->setObjectName(QString::fromUtf8("sta_ready_1"));
        sta_ready_1->setFont(font1);
        sta_ready_1->setStyleSheet(QString::fromUtf8(""));
        sta_ready_1->setScaledContents(true);

        gridLayout->addWidget(sta_ready_1, 0, 4, 1, 1);


        horizontalLayout_11->addLayout(gridLayout);


        verticalLayout->addWidget(groupBox_14);

        gridLayout_2 = new QGridLayout();
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        label_3 = new QLabel(groupBox);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        label_3->setFont(font1);

        gridLayout_2->addWidget(label_3, 0, 2, 1, 1);

        module_temp_1 = new QLabel(groupBox);
        module_temp_1->setObjectName(QString::fromUtf8("module_temp_1"));
        module_temp_1->setMaximumSize(QSize(16777215, 40));
        QFont font2;
        font2.setPointSize(14);
        module_temp_1->setFont(font2);
        module_temp_1->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(module_temp_1, 0, 1, 1, 1);

        label_28 = new QLabel(groupBox);
        label_28->setObjectName(QString::fromUtf8("label_28"));
        label_28->setFont(font1);

        gridLayout_2->addWidget(label_28, 2, 0, 1, 1);

        label_10 = new QLabel(groupBox);
        label_10->setObjectName(QString::fromUtf8("label_10"));
        label_10->setFont(font1);

        gridLayout_2->addWidget(label_10, 0, 0, 1, 1);

        hpa_input_power_1 = new QLabel(groupBox);
        hpa_input_power_1->setObjectName(QString::fromUtf8("hpa_input_power_1"));
        hpa_input_power_1->setMaximumSize(QSize(16777215, 40));
        hpa_input_power_1->setFont(font2);
        hpa_input_power_1->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(hpa_input_power_1, 2, 1, 1, 1);

        carrier_power_1 = new QLabel(groupBox);
        carrier_power_1->setObjectName(QString::fromUtf8("carrier_power_1"));
        carrier_power_1->setMinimumSize(QSize(180, 0));
        carrier_power_1->setMaximumSize(QSize(16777215, 40));
        carrier_power_1->setFont(font2);
        carrier_power_1->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(carrier_power_1, 4, 1, 1, 1);

        label_16 = new QLabel(groupBox);
        label_16->setObjectName(QString::fromUtf8("label_16"));
        label_16->setFont(font1);

        gridLayout_2->addWidget(label_16, 4, 0, 1, 1);

        output_power_1 = new QLabel(groupBox);
        output_power_1->setObjectName(QString::fromUtf8("output_power_1"));
        output_power_1->setMaximumSize(QSize(16777215, 40));
        output_power_1->setFont(font2);
        output_power_1->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(output_power_1, 0, 3, 1, 1);

        label_4 = new QLabel(groupBox);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setFont(font1);

        gridLayout_2->addWidget(label_4, 2, 2, 1, 1);

        hpa_output_power_1 = new QLabel(groupBox);
        hpa_output_power_1->setObjectName(QString::fromUtf8("hpa_output_power_1"));
        hpa_output_power_1->setMaximumSize(QSize(16777215, 40));
        hpa_output_power_1->setFont(font2);
        hpa_output_power_1->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(hpa_output_power_1, 2, 3, 1, 1);

        label_41 = new QLabel(groupBox);
        label_41->setObjectName(QString::fromUtf8("label_41"));
        label_41->setFont(font1);

        gridLayout_2->addWidget(label_41, 4, 2, 1, 1);

        wave_len_1 = new QLabel(groupBox);
        wave_len_1->setObjectName(QString::fromUtf8("wave_len_1"));
        wave_len_1->setMinimumSize(QSize(180, 0));
        wave_len_1->setMaximumSize(QSize(16777215, 40));
        wave_len_1->setFont(font2);
        wave_len_1->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(wave_len_1, 4, 3, 1, 1);


        verticalLayout->addLayout(gridLayout_2);

        verticalLayout->setStretch(0, 1);
        verticalLayout->setStretch(1, 6);

        verticalLayout_3->addWidget(groupBox);

        groupBox_3 = new QGroupBox(FormCombinedDev);
        groupBox_3->setObjectName(QString::fromUtf8("groupBox_3"));
        horizontalLayout_12 = new QHBoxLayout(groupBox_3);
        horizontalLayout_12->setSpacing(6);
        horizontalLayout_12->setObjectName(QString::fromUtf8("horizontalLayout_12"));
        horizontalLayout_12->setContentsMargins(6, 20, 6, 6);
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        label_11 = new QLabel(groupBox_3);
        label_11->setObjectName(QString::fromUtf8("label_11"));
        label_11->setFont(font2);

        horizontalLayout->addWidget(label_11);

        ctrl_module_status_1 = new QComboBox(groupBox_3);
        ctrl_module_status_1->setObjectName(QString::fromUtf8("ctrl_module_status_1"));
        ctrl_module_status_1->setFont(font2);

        horizontalLayout->addWidget(ctrl_module_status_1);

        btnSet_1 = new QPushButton(groupBox_3);
        btnSet_1->setObjectName(QString::fromUtf8("btnSet_1"));
        btnSet_1->setFont(font2);

        horizontalLayout->addWidget(btnSet_1);

        horizontalLayout->setStretch(0, 1);
        horizontalLayout->setStretch(1, 1);
        horizontalLayout->setStretch(2, 1);

        horizontalLayout_12->addLayout(horizontalLayout);


        verticalLayout_3->addWidget(groupBox_3);

        groupBox_20 = new QGroupBox(FormCombinedDev);
        groupBox_20->setObjectName(QString::fromUtf8("groupBox_20"));
        horizontalLayout_33 = new QHBoxLayout(groupBox_20);
        horizontalLayout_33->setSpacing(6);
        horizontalLayout_33->setObjectName(QString::fromUtf8("horizontalLayout_33"));
        horizontalLayout_33->setContentsMargins(6, 20, 6, 6);
        label_38 = new QLabel(groupBox_20);
        label_38->setObjectName(QString::fromUtf8("label_38"));
        label_38->setFont(font2);

        horizontalLayout_33->addWidget(label_38);

        sta_mod_status_1 = new QLabel(groupBox_20);
        sta_mod_status_1->setObjectName(QString::fromUtf8("sta_mod_status_1"));
        sta_mod_status_1->setFont(font1);
        sta_mod_status_1->setStyleSheet(QString::fromUtf8(""));
        sta_mod_status_1->setScaledContents(true);

        horizontalLayout_33->addWidget(sta_mod_status_1);

        btnSet_11 = new QPushButton(groupBox_20);
        btnSet_11->setObjectName(QString::fromUtf8("btnSet_11"));
        btnSet_11->setFont(font2);

        horizontalLayout_33->addWidget(btnSet_11);

        btnSet_19 = new QPushButton(groupBox_20);
        btnSet_19->setObjectName(QString::fromUtf8("btnSet_19"));
        btnSet_19->setFont(font2);

        horizontalLayout_33->addWidget(btnSet_19);

        horizontalLayout_33->setStretch(2, 1);
        horizontalLayout_33->setStretch(3, 1);

        verticalLayout_3->addWidget(groupBox_20);

        groupBox_4 = new QGroupBox(FormCombinedDev);
        groupBox_4->setObjectName(QString::fromUtf8("groupBox_4"));
        horizontalLayout_13 = new QHBoxLayout(groupBox_4);
        horizontalLayout_13->setSpacing(6);
        horizontalLayout_13->setObjectName(QString::fromUtf8("horizontalLayout_13"));
        horizontalLayout_13->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        label_12 = new QLabel(groupBox_4);
        label_12->setObjectName(QString::fromUtf8("label_12"));
        label_12->setFont(font2);

        horizontalLayout_2->addWidget(label_12);

        ctrl_output_power_1 = new QLineEdit(groupBox_4);
        ctrl_output_power_1->setObjectName(QString::fromUtf8("ctrl_output_power_1"));
        ctrl_output_power_1->setFont(font2);

        horizontalLayout_2->addWidget(ctrl_output_power_1);

        btnSet_2 = new QPushButton(groupBox_4);
        btnSet_2->setObjectName(QString::fromUtf8("btnSet_2"));
        btnSet_2->setFont(font2);

        horizontalLayout_2->addWidget(btnSet_2);

        horizontalLayout_2->setStretch(0, 1);
        horizontalLayout_2->setStretch(1, 1);
        horizontalLayout_2->setStretch(2, 1);

        horizontalLayout_13->addLayout(horizontalLayout_2);


        verticalLayout_3->addWidget(groupBox_4);

        groupBox_16 = new QGroupBox(FormCombinedDev);
        groupBox_16->setObjectName(QString::fromUtf8("groupBox_16"));
        horizontalLayout_25 = new QHBoxLayout(groupBox_16);
        horizontalLayout_25->setSpacing(6);
        horizontalLayout_25->setObjectName(QString::fromUtf8("horizontalLayout_25"));
        horizontalLayout_25->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_26 = new QHBoxLayout();
        horizontalLayout_26->setObjectName(QString::fromUtf8("horizontalLayout_26"));
        label_34 = new QLabel(groupBox_16);
        label_34->setObjectName(QString::fromUtf8("label_34"));
        label_34->setFont(font2);

        horizontalLayout_26->addWidget(label_34);

        sta_hpa_workmode_1 = new QLineEdit(groupBox_16);
        sta_hpa_workmode_1->setObjectName(QString::fromUtf8("sta_hpa_workmode_1"));
        sta_hpa_workmode_1->setFont(font2);

        horizontalLayout_26->addWidget(sta_hpa_workmode_1);

        ctrl_hpa_workmode_1 = new QComboBox(groupBox_16);
        ctrl_hpa_workmode_1->setObjectName(QString::fromUtf8("ctrl_hpa_workmode_1"));
        ctrl_hpa_workmode_1->setFont(font2);

        horizontalLayout_26->addWidget(ctrl_hpa_workmode_1);

        btnSet_13 = new QPushButton(groupBox_16);
        btnSet_13->setObjectName(QString::fromUtf8("btnSet_13"));
        btnSet_13->setFont(font2);

        horizontalLayout_26->addWidget(btnSet_13);

        horizontalLayout_26->setStretch(1, 1);
        horizontalLayout_26->setStretch(2, 1);
        horizontalLayout_26->setStretch(3, 1);

        horizontalLayout_25->addLayout(horizontalLayout_26);


        verticalLayout_3->addWidget(groupBox_16);

        groupBox_5 = new QGroupBox(FormCombinedDev);
        groupBox_5->setObjectName(QString::fromUtf8("groupBox_5"));
        horizontalLayout_16 = new QHBoxLayout(groupBox_5);
        horizontalLayout_16->setSpacing(6);
        horizontalLayout_16->setObjectName(QString::fromUtf8("horizontalLayout_16"));
        horizontalLayout_16->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        label_13 = new QLabel(groupBox_5);
        label_13->setObjectName(QString::fromUtf8("label_13"));
        label_13->setFont(font2);

        horizontalLayout_3->addWidget(label_13);

        ctrl_hpa_output_power_1 = new QLineEdit(groupBox_5);
        ctrl_hpa_output_power_1->setObjectName(QString::fromUtf8("ctrl_hpa_output_power_1"));
        ctrl_hpa_output_power_1->setFont(font2);

        horizontalLayout_3->addWidget(ctrl_hpa_output_power_1);

        btnSet_3 = new QPushButton(groupBox_5);
        btnSet_3->setObjectName(QString::fromUtf8("btnSet_3"));
        btnSet_3->setFont(font2);

        horizontalLayout_3->addWidget(btnSet_3);

        horizontalLayout_3->setStretch(0, 1);
        horizontalLayout_3->setStretch(1, 1);
        horizontalLayout_3->setStretch(2, 1);

        horizontalLayout_16->addLayout(horizontalLayout_3);


        verticalLayout_3->addWidget(groupBox_5);

        groupBox_18 = new QGroupBox(FormCombinedDev);
        groupBox_18->setObjectName(QString::fromUtf8("groupBox_18"));
        horizontalLayout_27 = new QHBoxLayout(groupBox_18);
        horizontalLayout_27->setSpacing(6);
        horizontalLayout_27->setObjectName(QString::fromUtf8("horizontalLayout_27"));
        horizontalLayout_27->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_28 = new QHBoxLayout();
        horizontalLayout_28->setObjectName(QString::fromUtf8("horizontalLayout_28"));
        label_35 = new QLabel(groupBox_18);
        label_35->setObjectName(QString::fromUtf8("label_35"));
        label_35->setFont(font2);

        horizontalLayout_28->addWidget(label_35);

        sta_current_1 = new QLineEdit(groupBox_18);
        sta_current_1->setObjectName(QString::fromUtf8("sta_current_1"));
        sta_current_1->setFont(font2);

        horizontalLayout_28->addWidget(sta_current_1);

        ctrl_current_1 = new QLineEdit(groupBox_18);
        ctrl_current_1->setObjectName(QString::fromUtf8("ctrl_current_1"));
        ctrl_current_1->setFont(font2);

        horizontalLayout_28->addWidget(ctrl_current_1);

        btnSet_16 = new QPushButton(groupBox_18);
        btnSet_16->setObjectName(QString::fromUtf8("btnSet_16"));
        btnSet_16->setFont(font2);

        horizontalLayout_28->addWidget(btnSet_16);

        horizontalLayout_28->setStretch(0, 1);
        horizontalLayout_28->setStretch(1, 1);
        horizontalLayout_28->setStretch(2, 1);
        horizontalLayout_28->setStretch(3, 1);

        horizontalLayout_27->addLayout(horizontalLayout_28);


        verticalLayout_3->addWidget(groupBox_18);

        groupBox_6 = new QGroupBox(FormCombinedDev);
        groupBox_6->setObjectName(QString::fromUtf8("groupBox_6"));
        horizontalLayout_17 = new QHBoxLayout(groupBox_6);
        horizontalLayout_17->setSpacing(6);
        horizontalLayout_17->setObjectName(QString::fromUtf8("horizontalLayout_17"));
        horizontalLayout_17->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        label_14 = new QLabel(groupBox_6);
        label_14->setObjectName(QString::fromUtf8("label_14"));
        label_14->setFont(font2);

        horizontalLayout_4->addWidget(label_14);

        wave_length_1 = new QLineEdit(groupBox_6);
        wave_length_1->setObjectName(QString::fromUtf8("wave_length_1"));
        wave_length_1->setFont(font2);

        horizontalLayout_4->addWidget(wave_length_1);

        ctrl_channel_num_1 = new QComboBox(groupBox_6);
        ctrl_channel_num_1->setObjectName(QString::fromUtf8("ctrl_channel_num_1"));
        ctrl_channel_num_1->setFont(font2);

        horizontalLayout_4->addWidget(ctrl_channel_num_1);

        btnSet_4 = new QPushButton(groupBox_6);
        btnSet_4->setObjectName(QString::fromUtf8("btnSet_4"));
        btnSet_4->setFont(font2);

        horizontalLayout_4->addWidget(btnSet_4);

        horizontalLayout_4->setStretch(1, 1);
        horizontalLayout_4->setStretch(3, 1);

        horizontalLayout_17->addLayout(horizontalLayout_4);


        verticalLayout_3->addWidget(groupBox_6);

        groupBox_7 = new QGroupBox(FormCombinedDev);
        groupBox_7->setObjectName(QString::fromUtf8("groupBox_7"));
        horizontalLayout_18 = new QHBoxLayout(groupBox_7);
        horizontalLayout_18->setSpacing(6);
        horizontalLayout_18->setObjectName(QString::fromUtf8("horizontalLayout_18"));
        horizontalLayout_18->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        label_23 = new QLabel(groupBox_7);
        label_23->setObjectName(QString::fromUtf8("label_23"));
        label_23->setFont(font2);

        horizontalLayout_5->addWidget(label_23);

        ctrl_freq_compensate_1 = new QLineEdit(groupBox_7);
        ctrl_freq_compensate_1->setObjectName(QString::fromUtf8("ctrl_freq_compensate_1"));
        ctrl_freq_compensate_1->setFont(font2);

        horizontalLayout_5->addWidget(ctrl_freq_compensate_1);

        btnSet_23 = new QPushButton(groupBox_7);
        btnSet_23->setObjectName(QString::fromUtf8("btnSet_23"));
        btnSet_23->setFont(font2);

        horizontalLayout_5->addWidget(btnSet_23);

        horizontalLayout_5->setStretch(0, 1);
        horizontalLayout_5->setStretch(1, 1);
        horizontalLayout_5->setStretch(2, 1);

        horizontalLayout_18->addLayout(horizontalLayout_5);


        verticalLayout_3->addWidget(groupBox_7);

        groupBox_21 = new QGroupBox(FormCombinedDev);
        groupBox_21->setObjectName(QString::fromUtf8("groupBox_21"));
        horizontalLayout_34 = new QHBoxLayout(groupBox_21);
        horizontalLayout_34->setSpacing(6);
        horizontalLayout_34->setObjectName(QString::fromUtf8("horizontalLayout_34"));
        horizontalLayout_34->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_35 = new QHBoxLayout();
        horizontalLayout_35->setObjectName(QString::fromUtf8("horizontalLayout_35"));
        label_39 = new QLabel(groupBox_21);
        label_39->setObjectName(QString::fromUtf8("label_39"));
        label_39->setFont(font2);

        horizontalLayout_35->addWidget(label_39);

        ctrl_wave_compensate_1 = new QLineEdit(groupBox_21);
        ctrl_wave_compensate_1->setObjectName(QString::fromUtf8("ctrl_wave_compensate_1"));
        ctrl_wave_compensate_1->setFont(font2);

        horizontalLayout_35->addWidget(ctrl_wave_compensate_1);

        btnSet_20 = new QPushButton(groupBox_21);
        btnSet_20->setObjectName(QString::fromUtf8("btnSet_20"));
        btnSet_20->setFont(font2);

        horizontalLayout_35->addWidget(btnSet_20);

        horizontalLayout_35->setStretch(0, 1);
        horizontalLayout_35->setStretch(1, 1);
        horizontalLayout_35->setStretch(2, 1);

        horizontalLayout_34->addLayout(horizontalLayout_35);


        verticalLayout_3->addWidget(groupBox_21);

        groupBox_23 = new QGroupBox(FormCombinedDev);
        groupBox_23->setObjectName(QString::fromUtf8("groupBox_23"));
        horizontalLayout_38 = new QHBoxLayout(groupBox_23);
        horizontalLayout_38->setSpacing(6);
        horizontalLayout_38->setObjectName(QString::fromUtf8("horizontalLayout_38"));
        horizontalLayout_38->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_39 = new QHBoxLayout();
        horizontalLayout_39->setObjectName(QString::fromUtf8("horizontalLayout_39"));
        label_43 = new QLabel(groupBox_23);
        label_43->setObjectName(QString::fromUtf8("label_43"));
        label_43->setFont(font2);

        horizontalLayout_39->addWidget(label_43);

        ctrl_freq_1 = new QLineEdit(groupBox_23);
        ctrl_freq_1->setObjectName(QString::fromUtf8("ctrl_freq_1"));
        ctrl_freq_1->setFont(font2);

        horizontalLayout_39->addWidget(ctrl_freq_1);

        btnSet_22 = new QPushButton(groupBox_23);
        btnSet_22->setObjectName(QString::fromUtf8("btnSet_22"));
        btnSet_22->setFont(font2);

        horizontalLayout_39->addWidget(btnSet_22);

        horizontalLayout_39->setStretch(0, 1);
        horizontalLayout_39->setStretch(1, 1);
        horizontalLayout_39->setStretch(2, 1);

        horizontalLayout_38->addLayout(horizontalLayout_39);


        verticalLayout_3->addWidget(groupBox_23);

        groupBox_25 = new QGroupBox(FormCombinedDev);
        groupBox_25->setObjectName(QString::fromUtf8("groupBox_25"));
        gridLayout_5 = new QGridLayout(groupBox_25);
        gridLayout_5->setObjectName(QString::fromUtf8("gridLayout_5"));
        ctrl_start_bin = new QLineEdit(groupBox_25);
        ctrl_start_bin->setObjectName(QString::fromUtf8("ctrl_start_bin"));
        ctrl_start_bin->setFont(font2);

        gridLayout_5->addWidget(ctrl_start_bin, 0, 5, 1, 1);

        btnSet_26 = new QPushButton(groupBox_25);
        btnSet_26->setObjectName(QString::fromUtf8("btnSet_26"));
        btnSet_26->setFont(font2);

        gridLayout_5->addWidget(btnSet_26, 0, 9, 1, 1);

        sta_raser_on = new QLabel(groupBox_25);
        sta_raser_on->setObjectName(QString::fromUtf8("sta_raser_on"));
        sta_raser_on->setFont(font1);
        sta_raser_on->setStyleSheet(QString::fromUtf8(""));
        sta_raser_on->setScaledContents(true);

        gridLayout_5->addWidget(sta_raser_on, 0, 6, 1, 1);

        btnSet_27 = new QPushButton(groupBox_25);
        btnSet_27->setObjectName(QString::fromUtf8("btnSet_27"));
        btnSet_27->setFont(font2);

        gridLayout_5->addWidget(btnSet_27, 0, 7, 1, 1);

        sta_scanstart = new QLabel(groupBox_25);
        sta_scanstart->setObjectName(QString::fromUtf8("sta_scanstart"));
        sta_scanstart->setFont(font1);
        sta_scanstart->setStyleSheet(QString::fromUtf8(""));
        sta_scanstart->setScaledContents(true);

        gridLayout_5->addWidget(sta_scanstart, 0, 8, 1, 1);

        sta_raser_off = new QLabel(groupBox_25);
        sta_raser_off->setObjectName(QString::fromUtf8("sta_raser_off"));
        sta_raser_off->setFont(font1);
        sta_raser_off->setStyleSheet(QString::fromUtf8(""));
        sta_raser_off->setScaledContents(true);

        gridLayout_5->addWidget(sta_raser_off, 0, 10, 1, 1);

        btnSet_28 = new QPushButton(groupBox_25);
        btnSet_28->setObjectName(QString::fromUtf8("btnSet_28"));
        btnSet_28->setFont(font2);

        gridLayout_5->addWidget(btnSet_28, 0, 11, 1, 1);

        label_47 = new QLabel(groupBox_25);
        label_47->setObjectName(QString::fromUtf8("label_47"));
        label_47->setFont(font2);

        gridLayout_5->addWidget(label_47, 0, 4, 1, 1);

        gridLayout_5->setColumnStretch(0, 1);
        gridLayout_5->setColumnStretch(1, 1);
        gridLayout_5->setColumnStretch(2, 1);
        gridLayout_5->setColumnStretch(3, 1);
        gridLayout_5->setColumnStretch(4, 1);
        gridLayout_5->setColumnStretch(5, 1);

        verticalLayout_3->addWidget(groupBox_25);

        groupBox_26 = new QGroupBox(FormCombinedDev);
        groupBox_26->setObjectName(QString::fromUtf8("groupBox_26"));
        verticalLayout_5 = new QVBoxLayout(groupBox_26);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        gridLayout_6 = new QGridLayout();
        gridLayout_6->setObjectName(QString::fromUtf8("gridLayout_6"));
        ctrl_start_freq = new QLineEdit(groupBox_26);
        ctrl_start_freq->setObjectName(QString::fromUtf8("ctrl_start_freq"));
        ctrl_start_freq->setFont(font2);

        gridLayout_6->addWidget(ctrl_start_freq, 0, 1, 1, 1);

        ctrl_end_freq = new QLineEdit(groupBox_26);
        ctrl_end_freq->setObjectName(QString::fromUtf8("ctrl_end_freq"));
        ctrl_end_freq->setFont(font2);

        gridLayout_6->addWidget(ctrl_end_freq, 0, 3, 1, 1);

        label_45 = new QLabel(groupBox_26);
        label_45->setObjectName(QString::fromUtf8("label_45"));
        label_45->setFont(font2);

        gridLayout_6->addWidget(label_45, 0, 2, 1, 1);

        label_44 = new QLabel(groupBox_26);
        label_44->setObjectName(QString::fromUtf8("label_44"));
        label_44->setFont(font2);

        gridLayout_6->addWidget(label_44, 0, 0, 1, 1);

        label_48 = new QLabel(groupBox_26);
        label_48->setObjectName(QString::fromUtf8("label_48"));
        label_48->setFont(font2);

        gridLayout_6->addWidget(label_48, 1, 0, 1, 1);

        ctrl_scan_rate = new QLineEdit(groupBox_26);
        ctrl_scan_rate->setObjectName(QString::fromUtf8("ctrl_scan_rate"));
        ctrl_scan_rate->setFont(font2);

        gridLayout_6->addWidget(ctrl_scan_rate, 1, 1, 1, 1);

        btnSet_5 = new QPushButton(groupBox_26);
        btnSet_5->setObjectName(QString::fromUtf8("btnSet_5"));
        btnSet_5->setFont(font2);

        gridLayout_6->addWidget(btnSet_5, 1, 3, 1, 1);


        verticalLayout_5->addLayout(gridLayout_6);


        verticalLayout_3->addWidget(groupBox_26);

        groupBox_12 = new QGroupBox(FormCombinedDev);
        groupBox_12->setObjectName(QString::fromUtf8("groupBox_12"));
        horizontalLayout_14 = new QHBoxLayout(groupBox_12);
        horizontalLayout_14->setSpacing(6);
        horizontalLayout_14->setObjectName(QString::fromUtf8("horizontalLayout_14"));
        horizontalLayout_14->setContentsMargins(6, 20, 6, 6);
        sta_open_prbs = new QLabel(groupBox_12);
        sta_open_prbs->setObjectName(QString::fromUtf8("sta_open_prbs"));
        sta_open_prbs->setFont(font1);
        sta_open_prbs->setStyleSheet(QString::fromUtf8(""));
        sta_open_prbs->setScaledContents(true);

        horizontalLayout_14->addWidget(sta_open_prbs);

        btnSet_10 = new QPushButton(groupBox_12);
        btnSet_10->setObjectName(QString::fromUtf8("btnSet_10"));
        btnSet_10->setFont(font2);

        horizontalLayout_14->addWidget(btnSet_10);

        btnSet_12 = new QPushButton(groupBox_12);
        btnSet_12->setObjectName(QString::fromUtf8("btnSet_12"));
        btnSet_12->setFont(font2);

        horizontalLayout_14->addWidget(btnSet_12);

        horizontalLayout_14->setStretch(1, 1);
        horizontalLayout_14->setStretch(2, 1);

        verticalLayout_3->addWidget(groupBox_12);

        horizontalLayout_10 = new QHBoxLayout();
        horizontalLayout_10->setSpacing(6);
        horizontalLayout_10->setObjectName(QString::fromUtf8("horizontalLayout_10"));
        label = new QLabel(FormCombinedDev);
        label->setObjectName(QString::fromUtf8("label"));
        label->setFont(font2);
        label->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_10->addWidget(label);

        comboBox_light = new QComboBox(FormCombinedDev);
        comboBox_light->setObjectName(QString::fromUtf8("comboBox_light"));

        horizontalLayout_10->addWidget(comboBox_light);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_10->addItem(horizontalSpacer_5);

        btnAmplifier = new QPushButton(FormCombinedDev);
        btnAmplifier->setObjectName(QString::fromUtf8("btnAmplifier"));
        btnAmplifier->setFont(font2);

        horizontalLayout_10->addWidget(btnAmplifier);

        btnRecordData = new QPushButton(FormCombinedDev);
        btnRecordData->setObjectName(QString::fromUtf8("btnRecordData"));
        btnRecordData->setFont(font2);

        horizontalLayout_10->addWidget(btnRecordData);

        btnOpenData = new QPushButton(FormCombinedDev);
        btnOpenData->setObjectName(QString::fromUtf8("btnOpenData"));
        btnOpenData->setFont(font2);

        horizontalLayout_10->addWidget(btnOpenData);


        verticalLayout_3->addLayout(horizontalLayout_10);


        horizontalLayout_41->addLayout(verticalLayout_3);

        verticalLayout_6 = new QVBoxLayout();
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        groupBox_2 = new QGroupBox(FormCombinedDev);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        groupBox_2->setFont(font);
        groupBox_2->setStyleSheet(QString::fromUtf8("QGroupBox#groupBox_2::title{\n"
"	font-weight:bold; \n"
"	font-size:30px; \n"
"}"));
        verticalLayout_2 = new QVBoxLayout(groupBox_2);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(-1, 40, -1, -1);
        groupBox_15 = new QGroupBox(groupBox_2);
        groupBox_15->setObjectName(QString::fromUtf8("groupBox_15"));
        horizontalLayout_23 = new QHBoxLayout(groupBox_15);
        horizontalLayout_23->setObjectName(QString::fromUtf8("horizontalLayout_23"));
        gridLayout_3 = new QGridLayout();
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        label_22 = new QLabel(groupBox_15);
        label_22->setObjectName(QString::fromUtf8("label_22"));
        label_22->setFont(font2);

        gridLayout_3->addWidget(label_22, 0, 9, 1, 1);

        sta_ready_2 = new QLabel(groupBox_15);
        sta_ready_2->setObjectName(QString::fromUtf8("sta_ready_2"));
        sta_ready_2->setFont(font2);
        sta_ready_2->setStyleSheet(QString::fromUtf8(""));
        sta_ready_2->setScaledContents(true);

        gridLayout_3->addWidget(sta_ready_2, 0, 4, 1, 1);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_3, 0, 2, 1, 1);

        label_20 = new QLabel(groupBox_15);
        label_20->setObjectName(QString::fromUtf8("label_20"));
        label_20->setFont(font2);

        gridLayout_3->addWidget(label_20, 0, 0, 1, 1);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_4, 0, 8, 1, 1);

        label_6 = new QLabel(groupBox_15);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        label_6->setFont(font2);

        gridLayout_3->addWidget(label_6, 0, 3, 1, 1);

        horizontalSpacer_7 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_7, 0, 5, 1, 1);

        sta_reset_2 = new QLabel(groupBox_15);
        sta_reset_2->setObjectName(QString::fromUtf8("sta_reset_2"));
        sta_reset_2->setFont(font2);
        sta_reset_2->setStyleSheet(QString::fromUtf8(""));
        sta_reset_2->setScaledContents(true);

        gridLayout_3->addWidget(sta_reset_2, 0, 1, 1, 1);

        sta_trans_2 = new QLabel(groupBox_15);
        sta_trans_2->setObjectName(QString::fromUtf8("sta_trans_2"));
        sta_trans_2->setFont(font2);
        sta_trans_2->setStyleSheet(QString::fromUtf8(""));
        sta_trans_2->setScaledContents(true);

        gridLayout_3->addWidget(sta_trans_2, 0, 7, 1, 1);

        label_7 = new QLabel(groupBox_15);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        label_7->setFont(font2);

        gridLayout_3->addWidget(label_7, 0, 6, 1, 1);

        sta_amplifier_status_2 = new QLabel(groupBox_15);
        sta_amplifier_status_2->setObjectName(QString::fromUtf8("sta_amplifier_status_2"));
        sta_amplifier_status_2->setMinimumSize(QSize(32, 32));
        sta_amplifier_status_2->setMaximumSize(QSize(32, 32));
        sta_amplifier_status_2->setFont(font2);
        sta_amplifier_status_2->setStyleSheet(QString::fromUtf8(""));
        sta_amplifier_status_2->setScaledContents(true);

        gridLayout_3->addWidget(sta_amplifier_status_2, 0, 10, 1, 1);


        horizontalLayout_23->addLayout(gridLayout_3);


        verticalLayout_2->addWidget(groupBox_15);

        gridLayout_4 = new QGridLayout();
        gridLayout_4->setObjectName(QString::fromUtf8("gridLayout_4"));
        bit_count_2 = new QLabel(groupBox_2);
        bit_count_2->setObjectName(QString::fromUtf8("bit_count_2"));
        bit_count_2->setMaximumSize(QSize(16777215, 40));
        bit_count_2->setFont(font2);
        bit_count_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_4->addWidget(bit_count_2, 10, 1, 1, 1);

        label_18 = new QLabel(groupBox_2);
        label_18->setObjectName(QString::fromUtf8("label_18"));
        label_18->setFont(font2);

        gridLayout_4->addWidget(label_18, 6, 0, 1, 1);

        evm_2 = new QLabel(groupBox_2);
        evm_2->setObjectName(QString::fromUtf8("evm_2"));
        evm_2->setMaximumSize(QSize(16777215, 40));
        evm_2->setFont(font2);
        evm_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_4->addWidget(evm_2, 8, 1, 1, 1);

        freq_offset_2 = new QLabel(groupBox_2);
        freq_offset_2->setObjectName(QString::fromUtf8("freq_offset_2"));
        freq_offset_2->setMaximumSize(QSize(16777215, 40));
        freq_offset_2->setFont(font2);
        freq_offset_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_4->addWidget(freq_offset_2, 6, 1, 1, 1);

        label_25 = new QLabel(groupBox_2);
        label_25->setObjectName(QString::fromUtf8("label_25"));
        label_25->setFont(font2);

        gridLayout_4->addWidget(label_25, 10, 0, 1, 1);

        hpa_input_power_2 = new QLabel(groupBox_2);
        hpa_input_power_2->setObjectName(QString::fromUtf8("hpa_input_power_2"));
        hpa_input_power_2->setMaximumSize(QSize(16777215, 40));
        hpa_input_power_2->setFont(font2);
        hpa_input_power_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_4->addWidget(hpa_input_power_2, 2, 1, 1, 1);

        label_17 = new QLabel(groupBox_2);
        label_17->setObjectName(QString::fromUtf8("label_17"));
        label_17->setFont(font2);

        gridLayout_4->addWidget(label_17, 4, 0, 1, 1);

        label_15 = new QLabel(groupBox_2);
        label_15->setObjectName(QString::fromUtf8("label_15"));
        label_15->setFont(font2);

        gridLayout_4->addWidget(label_15, 0, 0, 1, 1);

        module_temp_2 = new QLabel(groupBox_2);
        module_temp_2->setObjectName(QString::fromUtf8("module_temp_2"));
        module_temp_2->setMaximumSize(QSize(16777215, 40));
        module_temp_2->setFont(font2);
        module_temp_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_4->addWidget(module_temp_2, 0, 1, 1, 1);

        carrier_power_2 = new QLabel(groupBox_2);
        carrier_power_2->setObjectName(QString::fromUtf8("carrier_power_2"));
        carrier_power_2->setMinimumSize(QSize(180, 0));
        carrier_power_2->setMaximumSize(QSize(16777215, 40));
        carrier_power_2->setFont(font2);
        carrier_power_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_4->addWidget(carrier_power_2, 4, 1, 1, 1);

        label_8 = new QLabel(groupBox_2);
        label_8->setObjectName(QString::fromUtf8("label_8"));
        label_8->setFont(font2);

        gridLayout_4->addWidget(label_8, 0, 2, 1, 1);

        label_31 = new QLabel(groupBox_2);
        label_31->setObjectName(QString::fromUtf8("label_31"));
        label_31->setFont(font2);

        gridLayout_4->addWidget(label_31, 2, 0, 1, 1);

        label_32 = new QLabel(groupBox_2);
        label_32->setObjectName(QString::fromUtf8("label_32"));
        label_32->setFont(font2);

        gridLayout_4->addWidget(label_32, 8, 0, 1, 1);

        output_power_2 = new QLabel(groupBox_2);
        output_power_2->setObjectName(QString::fromUtf8("output_power_2"));
        output_power_2->setMaximumSize(QSize(16777215, 40));
        output_power_2->setFont(font2);
        output_power_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_4->addWidget(output_power_2, 0, 3, 1, 1);

        label_9 = new QLabel(groupBox_2);
        label_9->setObjectName(QString::fromUtf8("label_9"));
        label_9->setFont(font2);

        gridLayout_4->addWidget(label_9, 2, 2, 1, 1);

        hpa_output_power_2 = new QLabel(groupBox_2);
        hpa_output_power_2->setObjectName(QString::fromUtf8("hpa_output_power_2"));
        hpa_output_power_2->setMaximumSize(QSize(16777215, 40));
        hpa_output_power_2->setFont(font2);
        hpa_output_power_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_4->addWidget(hpa_output_power_2, 2, 3, 1, 1);

        label_42 = new QLabel(groupBox_2);
        label_42->setObjectName(QString::fromUtf8("label_42"));
        label_42->setFont(font1);

        gridLayout_4->addWidget(label_42, 4, 2, 1, 1);

        wave_len_2 = new QLabel(groupBox_2);
        wave_len_2->setObjectName(QString::fromUtf8("wave_len_2"));
        wave_len_2->setMinimumSize(QSize(180, 0));
        wave_len_2->setMaximumSize(QSize(16777215, 40));
        wave_len_2->setFont(font2);
        wave_len_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_4->addWidget(wave_len_2, 4, 3, 1, 1);

        label_24 = new QLabel(groupBox_2);
        label_24->setObjectName(QString::fromUtf8("label_24"));
        label_24->setFont(font2);

        gridLayout_4->addWidget(label_24, 6, 2, 1, 1);

        snr_2 = new QLabel(groupBox_2);
        snr_2->setObjectName(QString::fromUtf8("snr_2"));
        snr_2->setMaximumSize(QSize(16777215, 40));
        snr_2->setFont(font2);
        snr_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_4->addWidget(snr_2, 6, 3, 1, 1);

        label_33 = new QLabel(groupBox_2);
        label_33->setObjectName(QString::fromUtf8("label_33"));
        label_33->setFont(font2);

        gridLayout_4->addWidget(label_33, 8, 2, 1, 1);

        error_code_rate_2 = new QLabel(groupBox_2);
        error_code_rate_2->setObjectName(QString::fromUtf8("error_code_rate_2"));
        error_code_rate_2->setMaximumSize(QSize(16777215, 40));
        error_code_rate_2->setFont(font2);
        error_code_rate_2->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout_4->addWidget(error_code_rate_2, 8, 3, 1, 1);


        verticalLayout_2->addLayout(gridLayout_4);

        verticalLayout_2->setStretch(0, 1);
        verticalLayout_2->setStretch(1, 11);

        verticalLayout_6->addWidget(groupBox_2);

        verticalLayout_4 = new QVBoxLayout();
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        groupBox_8 = new QGroupBox(FormCombinedDev);
        groupBox_8->setObjectName(QString::fromUtf8("groupBox_8"));
        horizontalLayout_19 = new QHBoxLayout(groupBox_8);
        horizontalLayout_19->setSpacing(6);
        horizontalLayout_19->setObjectName(QString::fromUtf8("horizontalLayout_19"));
        horizontalLayout_19->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        label_26 = new QLabel(groupBox_8);
        label_26->setObjectName(QString::fromUtf8("label_26"));
        label_26->setFont(font2);

        horizontalLayout_6->addWidget(label_26);

        ctrl_module_status_2 = new QComboBox(groupBox_8);
        ctrl_module_status_2->setObjectName(QString::fromUtf8("ctrl_module_status_2"));
        ctrl_module_status_2->setFont(font2);

        horizontalLayout_6->addWidget(ctrl_module_status_2);

        btnSet_6 = new QPushButton(groupBox_8);
        btnSet_6->setObjectName(QString::fromUtf8("btnSet_6"));
        btnSet_6->setFont(font2);

        horizontalLayout_6->addWidget(btnSet_6);

        horizontalLayout_6->setStretch(0, 1);
        horizontalLayout_6->setStretch(1, 1);
        horizontalLayout_6->setStretch(2, 1);

        horizontalLayout_19->addLayout(horizontalLayout_6);


        verticalLayout_4->addWidget(groupBox_8);

        groupBox_17 = new QGroupBox(FormCombinedDev);
        groupBox_17->setObjectName(QString::fromUtf8("groupBox_17"));
        horizontalLayout_30 = new QHBoxLayout(groupBox_17);
        horizontalLayout_30->setSpacing(6);
        horizontalLayout_30->setObjectName(QString::fromUtf8("horizontalLayout_30"));
        horizontalLayout_30->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_31 = new QHBoxLayout();
        horizontalLayout_31->setObjectName(QString::fromUtf8("horizontalLayout_31"));
        label_37 = new QLabel(groupBox_17);
        label_37->setObjectName(QString::fromUtf8("label_37"));
        label_37->setFont(font2);

        horizontalLayout_31->addWidget(label_37);

        sta_hpa_workmode_2 = new QLineEdit(groupBox_17);
        sta_hpa_workmode_2->setObjectName(QString::fromUtf8("sta_hpa_workmode_2"));
        sta_hpa_workmode_2->setFont(font2);

        horizontalLayout_31->addWidget(sta_hpa_workmode_2);

        ctrl_hpa_workmode_2 = new QComboBox(groupBox_17);
        ctrl_hpa_workmode_2->setObjectName(QString::fromUtf8("ctrl_hpa_workmode_2"));
        ctrl_hpa_workmode_2->setFont(font2);

        horizontalLayout_31->addWidget(ctrl_hpa_workmode_2);

        btnSet_18 = new QPushButton(groupBox_17);
        btnSet_18->setObjectName(QString::fromUtf8("btnSet_18"));
        btnSet_18->setFont(font2);

        horizontalLayout_31->addWidget(btnSet_18);

        horizontalLayout_31->setStretch(1, 1);
        horizontalLayout_31->setStretch(2, 1);
        horizontalLayout_31->setStretch(3, 1);

        horizontalLayout_30->addLayout(horizontalLayout_31);


        verticalLayout_4->addWidget(groupBox_17);

        groupBox_9 = new QGroupBox(FormCombinedDev);
        groupBox_9->setObjectName(QString::fromUtf8("groupBox_9"));
        horizontalLayout_20 = new QHBoxLayout(groupBox_9);
        horizontalLayout_20->setSpacing(6);
        horizontalLayout_20->setObjectName(QString::fromUtf8("horizontalLayout_20"));
        horizontalLayout_20->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_7 = new QHBoxLayout();
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        label_27 = new QLabel(groupBox_9);
        label_27->setObjectName(QString::fromUtf8("label_27"));
        label_27->setFont(font2);

        horizontalLayout_7->addWidget(label_27);

        ctrl_hpa_output_power_2 = new QLineEdit(groupBox_9);
        ctrl_hpa_output_power_2->setObjectName(QString::fromUtf8("ctrl_hpa_output_power_2"));
        ctrl_hpa_output_power_2->setFont(font2);

        horizontalLayout_7->addWidget(ctrl_hpa_output_power_2);

        btnSet_7 = new QPushButton(groupBox_9);
        btnSet_7->setObjectName(QString::fromUtf8("btnSet_7"));
        btnSet_7->setFont(font2);

        horizontalLayout_7->addWidget(btnSet_7);

        horizontalLayout_7->setStretch(0, 1);
        horizontalLayout_7->setStretch(1, 1);
        horizontalLayout_7->setStretch(2, 1);

        horizontalLayout_20->addLayout(horizontalLayout_7);


        verticalLayout_4->addWidget(groupBox_9);

        groupBox_19 = new QGroupBox(FormCombinedDev);
        groupBox_19->setObjectName(QString::fromUtf8("groupBox_19"));
        horizontalLayout_29 = new QHBoxLayout(groupBox_19);
        horizontalLayout_29->setSpacing(6);
        horizontalLayout_29->setObjectName(QString::fromUtf8("horizontalLayout_29"));
        horizontalLayout_29->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_32 = new QHBoxLayout();
        horizontalLayout_32->setObjectName(QString::fromUtf8("horizontalLayout_32"));
        label_36 = new QLabel(groupBox_19);
        label_36->setObjectName(QString::fromUtf8("label_36"));
        label_36->setFont(font2);

        horizontalLayout_32->addWidget(label_36);

        sta_current_2 = new QLineEdit(groupBox_19);
        sta_current_2->setObjectName(QString::fromUtf8("sta_current_2"));
        sta_current_2->setFont(font2);

        horizontalLayout_32->addWidget(sta_current_2);

        ctrl_current_2 = new QLineEdit(groupBox_19);
        ctrl_current_2->setObjectName(QString::fromUtf8("ctrl_current_2"));
        ctrl_current_2->setFont(font2);

        horizontalLayout_32->addWidget(ctrl_current_2);

        btnSet_17 = new QPushButton(groupBox_19);
        btnSet_17->setObjectName(QString::fromUtf8("btnSet_17"));
        btnSet_17->setFont(font2);

        horizontalLayout_32->addWidget(btnSet_17);

        horizontalLayout_32->setStretch(0, 1);
        horizontalLayout_32->setStretch(1, 1);
        horizontalLayout_32->setStretch(2, 1);
        horizontalLayout_32->setStretch(3, 1);

        horizontalLayout_29->addLayout(horizontalLayout_32);


        verticalLayout_4->addWidget(groupBox_19);

        groupBox_10 = new QGroupBox(FormCombinedDev);
        groupBox_10->setObjectName(QString::fromUtf8("groupBox_10"));
        horizontalLayout_21 = new QHBoxLayout(groupBox_10);
        horizontalLayout_21->setSpacing(6);
        horizontalLayout_21->setObjectName(QString::fromUtf8("horizontalLayout_21"));
        horizontalLayout_21->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setObjectName(QString::fromUtf8("horizontalLayout_8"));
        label_29 = new QLabel(groupBox_10);
        label_29->setObjectName(QString::fromUtf8("label_29"));
        label_29->setFont(font2);

        horizontalLayout_8->addWidget(label_29);

        wave_length_2 = new QLineEdit(groupBox_10);
        wave_length_2->setObjectName(QString::fromUtf8("wave_length_2"));
        wave_length_2->setFont(font2);

        horizontalLayout_8->addWidget(wave_length_2);

        ctrl_channel_num_2 = new QComboBox(groupBox_10);
        ctrl_channel_num_2->setObjectName(QString::fromUtf8("ctrl_channel_num_2"));
        ctrl_channel_num_2->setFont(font2);

        horizontalLayout_8->addWidget(ctrl_channel_num_2);

        btnSet_8 = new QPushButton(groupBox_10);
        btnSet_8->setObjectName(QString::fromUtf8("btnSet_8"));
        btnSet_8->setFont(font2);

        horizontalLayout_8->addWidget(btnSet_8);

        horizontalLayout_8->setStretch(1, 1);
        horizontalLayout_8->setStretch(2, 1);
        horizontalLayout_8->setStretch(3, 1);

        horizontalLayout_21->addLayout(horizontalLayout_8);


        verticalLayout_4->addWidget(groupBox_10);

        groupBox_11 = new QGroupBox(FormCombinedDev);
        groupBox_11->setObjectName(QString::fromUtf8("groupBox_11"));
        horizontalLayout_22 = new QHBoxLayout(groupBox_11);
        horizontalLayout_22->setSpacing(6);
        horizontalLayout_22->setObjectName(QString::fromUtf8("horizontalLayout_22"));
        horizontalLayout_22->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_9 = new QHBoxLayout();
        horizontalLayout_9->setObjectName(QString::fromUtf8("horizontalLayout_9"));
        label_30 = new QLabel(groupBox_11);
        label_30->setObjectName(QString::fromUtf8("label_30"));
        label_30->setFont(font2);

        horizontalLayout_9->addWidget(label_30);

        ctrl_freq_compensate_2 = new QLineEdit(groupBox_11);
        ctrl_freq_compensate_2->setObjectName(QString::fromUtf8("ctrl_freq_compensate_2"));
        ctrl_freq_compensate_2->setFont(font2);

        horizontalLayout_9->addWidget(ctrl_freq_compensate_2);

        btnSet_9 = new QPushButton(groupBox_11);
        btnSet_9->setObjectName(QString::fromUtf8("btnSet_9"));
        btnSet_9->setFont(font2);

        horizontalLayout_9->addWidget(btnSet_9);

        horizontalLayout_9->setStretch(0, 1);
        horizontalLayout_9->setStretch(1, 1);
        horizontalLayout_9->setStretch(2, 1);

        horizontalLayout_22->addLayout(horizontalLayout_9);


        verticalLayout_4->addWidget(groupBox_11);

        groupBox_22 = new QGroupBox(FormCombinedDev);
        groupBox_22->setObjectName(QString::fromUtf8("groupBox_22"));
        horizontalLayout_36 = new QHBoxLayout(groupBox_22);
        horizontalLayout_36->setSpacing(6);
        horizontalLayout_36->setObjectName(QString::fromUtf8("horizontalLayout_36"));
        horizontalLayout_36->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_37 = new QHBoxLayout();
        horizontalLayout_37->setObjectName(QString::fromUtf8("horizontalLayout_37"));
        label_40 = new QLabel(groupBox_22);
        label_40->setObjectName(QString::fromUtf8("label_40"));
        label_40->setFont(font2);

        horizontalLayout_37->addWidget(label_40);

        ctrl_wave_compensate_2 = new QLineEdit(groupBox_22);
        ctrl_wave_compensate_2->setObjectName(QString::fromUtf8("ctrl_wave_compensate_2"));
        ctrl_wave_compensate_2->setFont(font2);

        horizontalLayout_37->addWidget(ctrl_wave_compensate_2);

        btnSet_21 = new QPushButton(groupBox_22);
        btnSet_21->setObjectName(QString::fromUtf8("btnSet_21"));
        btnSet_21->setFont(font2);

        horizontalLayout_37->addWidget(btnSet_21);

        horizontalLayout_37->setStretch(0, 1);
        horizontalLayout_37->setStretch(1, 1);
        horizontalLayout_37->setStretch(2, 1);

        horizontalLayout_36->addLayout(horizontalLayout_37);


        verticalLayout_4->addWidget(groupBox_22);

        groupBox_24 = new QGroupBox(FormCombinedDev);
        groupBox_24->setObjectName(QString::fromUtf8("groupBox_24"));
        horizontalLayout_44 = new QHBoxLayout(groupBox_24);
        horizontalLayout_44->setSpacing(6);
        horizontalLayout_44->setObjectName(QString::fromUtf8("horizontalLayout_44"));
        horizontalLayout_44->setContentsMargins(6, 20, 6, 6);
        horizontalLayout_45 = new QHBoxLayout();
        horizontalLayout_45->setObjectName(QString::fromUtf8("horizontalLayout_45"));
        label_46 = new QLabel(groupBox_24);
        label_46->setObjectName(QString::fromUtf8("label_46"));
        label_46->setFont(font2);

        horizontalLayout_45->addWidget(label_46);

        ctrl_freq_2 = new QLineEdit(groupBox_24);
        ctrl_freq_2->setObjectName(QString::fromUtf8("ctrl_freq_2"));
        ctrl_freq_2->setFont(font2);

        horizontalLayout_45->addWidget(ctrl_freq_2);

        btnSet_25 = new QPushButton(groupBox_24);
        btnSet_25->setObjectName(QString::fromUtf8("btnSet_25"));
        btnSet_25->setFont(font2);

        horizontalLayout_45->addWidget(btnSet_25);

        horizontalLayout_45->setStretch(0, 1);
        horizontalLayout_45->setStretch(1, 1);
        horizontalLayout_45->setStretch(2, 1);

        horizontalLayout_44->addLayout(horizontalLayout_45);


        verticalLayout_4->addWidget(groupBox_24);

        groupBox_13 = new QGroupBox(FormCombinedDev);
        groupBox_13->setObjectName(QString::fromUtf8("groupBox_13"));
        horizontalLayout_15 = new QHBoxLayout(groupBox_13);
        horizontalLayout_15->setSpacing(6);
        horizontalLayout_15->setObjectName(QString::fromUtf8("horizontalLayout_15"));
        horizontalLayout_15->setContentsMargins(6, 6, 6, 9);
        sta_recv_prbs = new QLabel(groupBox_13);
        sta_recv_prbs->setObjectName(QString::fromUtf8("sta_recv_prbs"));
        sta_recv_prbs->setFont(font1);
        sta_recv_prbs->setStyleSheet(QString::fromUtf8(""));
        sta_recv_prbs->setScaledContents(true);
        sta_recv_prbs->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop);

        horizontalLayout_15->addWidget(sta_recv_prbs);

        btnSet_14 = new QPushButton(groupBox_13);
        btnSet_14->setObjectName(QString::fromUtf8("btnSet_14"));
        btnSet_14->setFont(font2);

        horizontalLayout_15->addWidget(btnSet_14);

        btnSet_15 = new QPushButton(groupBox_13);
        btnSet_15->setObjectName(QString::fromUtf8("btnSet_15"));
        btnSet_15->setFont(font2);

        horizontalLayout_15->addWidget(btnSet_15);

        horizontalLayout_15->setStretch(0, 1);
        horizontalLayout_15->setStretch(1, 2);
        horizontalLayout_15->setStretch(2, 2);

        verticalLayout_4->addWidget(groupBox_13);


        verticalLayout_6->addLayout(verticalLayout_4);

        verticalLayout_6->setStretch(0, 6);
        verticalLayout_6->setStretch(1, 8);

        horizontalLayout_41->addLayout(verticalLayout_6);

        horizontalLayout_41->setStretch(0, 1);
        horizontalLayout_41->setStretch(1, 1);

        retranslateUi(FormCombinedDev);

        QMetaObject::connectSlotsByName(FormCombinedDev);
    } // setupUi

    void retranslateUi(QWidget *FormCombinedDev)
    {
        FormCombinedDev->setWindowTitle(QCoreApplication::translate("FormCombinedDev", "Form", nullptr));
        groupBox_14->setTitle(QString());
        sta_amplifier_status_1->setText(QString());
        sta_trans_1->setText(QString());
        label_5->setText(QCoreApplication::translate("FormCombinedDev", "\351\200\232\344\277\241\357\274\232", nullptr));
        sta_reset_1->setText(QString());
        label_2->setText(QCoreApplication::translate("FormCombinedDev", "\345\260\261\347\273\252\357\274\232", nullptr));
        label_21->setText(QCoreApplication::translate("FormCombinedDev", "\345\212\237\346\224\276\357\274\232", nullptr));
        label_19->setText(QCoreApplication::translate("FormCombinedDev", "\345\244\215\344\275\215\357\274\232", nullptr));
        sta_ready_1->setText(QString());
        label_3->setText(QCoreApplication::translate("FormCombinedDev", "\345\205\211\346\250\241\345\235\227\350\276\223\345\207\272\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        module_temp_1->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_28->setText(QCoreApplication::translate("FormCombinedDev", "\351\253\230\345\212\237\346\224\276\350\276\223\345\205\245(dBm)\357\274\232", nullptr));
        label_10->setText(QCoreApplication::translate("FormCombinedDev", "\345\205\211\346\250\241\345\235\227\346\270\251\345\272\246(\342\204\203)\357\274\232", nullptr));
        hpa_input_power_1->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        carrier_power_1->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_16->setText(QCoreApplication::translate("FormCombinedDev", "\345\217\221\345\260\204\345\205\211\351\242\221\347\216\207(GHz)\357\274\232", nullptr));
        output_power_1->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_4->setText(QCoreApplication::translate("FormCombinedDev", "\351\253\230\345\212\237\346\224\276\350\276\223\345\207\272(dBm)\357\274\232", nullptr));
        hpa_output_power_1->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_41->setText(QCoreApplication::translate("FormCombinedDev", "\345\217\221\345\260\204\345\205\211\346\263\242\351\225\277\357\274\232", nullptr));
        wave_len_1->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        groupBox_3->setTitle(QString());
        label_11->setText(QCoreApplication::translate("FormCombinedDev", "\346\250\241\345\274\217\351\200\211\346\213\251\357\274\232", nullptr));
        btnSet_1->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_20->setTitle(QString());
        label_38->setText(QCoreApplication::translate("FormCombinedDev", "\346\277\200\345\205\211\345\237\272\345\270\246\350\260\203\345\210\266\357\274\232", nullptr));
        sta_mod_status_1->setText(QString());
        btnSet_11->setText(QCoreApplication::translate("FormCombinedDev", "\350\260\203\345\210\266\346\211\223\345\274\200", nullptr));
        btnSet_19->setText(QCoreApplication::translate("FormCombinedDev", "\350\260\203\345\210\266\345\205\263\351\227\255", nullptr));
        groupBox_4->setTitle(QString());
        label_12->setText(QCoreApplication::translate("FormCombinedDev", "\345\205\211\346\250\241\345\235\227\350\276\223\345\207\272\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        ctrl_output_power_1->setText(QCoreApplication::translate("FormCombinedDev", "0", nullptr));
        btnSet_2->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_16->setTitle(QString());
        label_34->setText(QCoreApplication::translate("FormCombinedDev", "\351\253\230\345\212\237\346\224\276\345\267\245\344\275\234\346\250\241\345\274\217\357\274\232", nullptr));
        btnSet_13->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_5->setTitle(QString());
        label_13->setText(QCoreApplication::translate("FormCombinedDev", "\351\253\230\345\212\237\346\224\276\350\276\223\345\207\272\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        ctrl_hpa_output_power_1->setText(QCoreApplication::translate("FormCombinedDev", "12", nullptr));
        btnSet_3->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_18->setTitle(QString());
        label_35->setText(QCoreApplication::translate("FormCombinedDev", "\351\253\230\345\212\237\346\224\276\346\263\265\346\265\246\347\224\265\346\265\201(mA)\357\274\232", nullptr));
        ctrl_current_1->setText(QCoreApplication::translate("FormCombinedDev", "100", nullptr));
        btnSet_16->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_6->setTitle(QString());
        label_14->setText(QCoreApplication::translate("FormCombinedDev", "\346\277\200\345\205\211\346\263\242\346\256\265(C13-C61)\357\274\232", nullptr));
        btnSet_4->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_7->setTitle(QString());
        label_23->setText(QCoreApplication::translate("FormCombinedDev", "\345\233\272\345\256\232\345\201\217\345\267\256(MHz)\357\274\232", nullptr));
        ctrl_freq_compensate_1->setText(QCoreApplication::translate("FormCombinedDev", "0", nullptr));
        btnSet_23->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_21->setTitle(QString());
        label_39->setText(QCoreApplication::translate("FormCombinedDev", "\345\217\221\345\260\204\346\263\242\351\225\277(nm)\357\274\232", nullptr));
        ctrl_wave_compensate_1->setText(QCoreApplication::translate("FormCombinedDev", "0", nullptr));
        btnSet_20->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_23->setTitle(QString());
        label_43->setText(QCoreApplication::translate("FormCombinedDev", "\345\217\221\345\260\204\351\242\221\347\216\207(GHz)\357\274\232", nullptr));
        ctrl_freq_1->setText(QCoreApplication::translate("FormCombinedDev", "0", nullptr));
        btnSet_22->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_25->setTitle(QString());
        ctrl_start_bin->setText(QCoreApplication::translate("FormCombinedDev", "0", nullptr));
        btnSet_26->setText(QCoreApplication::translate("FormCombinedDev", "\351\242\221\347\216\207\347\262\276\350\260\203", nullptr));
        sta_raser_on->setText(QString());
        btnSet_27->setText(QCoreApplication::translate("FormCombinedDev", "\346\277\200\345\205\211\345\231\250\345\277\275\347\225\245\345\205\263\351\227\255", nullptr));
        sta_scanstart->setText(QString());
        sta_raser_off->setText(QString());
        btnSet_28->setText(QCoreApplication::translate("FormCombinedDev", "\346\277\200\345\205\211\345\231\250\345\220\257\347\224\250\345\205\263\351\227\255", nullptr));
        label_47->setText(QCoreApplication::translate("FormCombinedDev", "\351\242\221\345\201\217(MHz)\357\274\232", nullptr));
        groupBox_26->setTitle(QString());
        ctrl_start_freq->setText(QCoreApplication::translate("FormCombinedDev", "0", nullptr));
        ctrl_end_freq->setText(QCoreApplication::translate("FormCombinedDev", "0", nullptr));
        label_45->setText(QCoreApplication::translate("FormCombinedDev", "\347\273\223\346\235\237\351\242\221\347\202\271(MHz)\357\274\232", nullptr));
        label_44->setText(QCoreApplication::translate("FormCombinedDev", "\345\274\200\345\247\213\351\242\221\347\202\271(MHz)\357\274\232", nullptr));
        label_48->setText(QCoreApplication::translate("FormCombinedDev", "\346\211\253\351\242\221\351\200\237\345\272\246(MHz/s)\357\274\232", nullptr));
        ctrl_scan_rate->setText(QCoreApplication::translate("FormCombinedDev", "0", nullptr));
        btnSet_5->setText(QCoreApplication::translate("FormCombinedDev", "\345\274\200\345\247\213\346\211\253\351\242\221", nullptr));
        groupBox_12->setTitle(QString());
        sta_open_prbs->setText(QString());
        btnSet_10->setText(QCoreApplication::translate("FormCombinedDev", "\346\211\223\345\274\200\347\272\277\350\267\257\344\276\247PRBS\345\217\221\346\225\260", nullptr));
        btnSet_12->setText(QCoreApplication::translate("FormCombinedDev", "\345\205\263\351\227\255\347\272\277\350\267\257\344\276\247PRBS\345\217\221\346\225\260", nullptr));
        label->setText(QCoreApplication::translate("FormCombinedDev", "\345\205\211\351\200\237\345\270\270\345\200\274\357\274\232", nullptr));
        btnAmplifier->setText(QCoreApplication::translate("FormCombinedDev", "\345\212\237\346\224\276\350\257\246\346\203\205", nullptr));
        btnRecordData->setText(QCoreApplication::translate("FormCombinedDev", "\345\274\200\345\247\213\350\256\260\345\275\225", nullptr));
        btnOpenData->setText(QCoreApplication::translate("FormCombinedDev", "\346\211\223\345\274\200\350\256\260\345\275\225\350\241\250", nullptr));
        groupBox_2->setTitle(QCoreApplication::translate("FormCombinedDev", "\346\216\245\346\224\266\347\253\257", nullptr));
        groupBox_15->setTitle(QString());
        label_22->setText(QCoreApplication::translate("FormCombinedDev", "\345\212\237\346\224\276\357\274\232", nullptr));
        sta_ready_2->setText(QString());
        label_20->setText(QCoreApplication::translate("FormCombinedDev", "\345\244\215\344\275\215\357\274\232", nullptr));
        label_6->setText(QCoreApplication::translate("FormCombinedDev", "\345\260\261\347\273\252\357\274\232", nullptr));
        sta_reset_2->setText(QString());
        sta_trans_2->setText(QString());
        label_7->setText(QCoreApplication::translate("FormCombinedDev", "\351\200\232\344\277\241\357\274\232", nullptr));
        sta_amplifier_status_2->setText(QString());
        bit_count_2->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_18->setText(QCoreApplication::translate("FormCombinedDev", "CFO\344\274\260\350\256\241\345\200\274(MHz)\357\274\232", nullptr));
        evm_2->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        freq_offset_2->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_25->setText(QCoreApplication::translate("FormCombinedDev", "\347\272\240\345\220\216\350\257\257\347\240\201\347\216\207\357\274\232", nullptr));
        hpa_input_power_2->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_17->setText(QCoreApplication::translate("FormCombinedDev", "\346\234\254\346\214\257\345\205\211\351\242\221\347\216\207(GHz)\357\274\232", nullptr));
        label_15->setText(QCoreApplication::translate("FormCombinedDev", "\345\205\211\346\250\241\345\235\227\346\270\251\345\272\246(\342\204\203)\357\274\232", nullptr));
        module_temp_2->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        carrier_power_2->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_8->setText(QCoreApplication::translate("FormCombinedDev", "\345\205\211\346\250\241\345\235\227\350\276\223\345\205\245\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        label_31->setText(QCoreApplication::translate("FormCombinedDev", "\344\275\216\345\231\252\346\224\276\350\276\223\345\205\245(dBm)\357\274\232", nullptr));
        label_32->setText(QCoreApplication::translate("FormCombinedDev", "EVM(%)\357\274\232", nullptr));
        output_power_2->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_9->setText(QCoreApplication::translate("FormCombinedDev", "\344\275\216\345\231\252\346\224\276\350\276\223\345\207\272(dBm)\357\274\232", nullptr));
        hpa_output_power_2->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_42->setText(QCoreApplication::translate("FormCombinedDev", "\346\234\254\346\214\257\345\205\211\346\263\242\351\225\277\357\274\232", nullptr));
        wave_len_2->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_24->setText(QCoreApplication::translate("FormCombinedDev", "\346\216\245\346\224\266\345\205\211\344\277\241\345\231\252\346\257\224(dB)\357\274\232", nullptr));
        snr_2->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        label_33->setText(QCoreApplication::translate("FormCombinedDev", "\347\272\240\345\211\215\350\257\257\347\240\201\347\216\207\357\274\232", nullptr));
        error_code_rate_2->setText(QCoreApplication::translate("FormCombinedDev", "TextLabel", nullptr));
        groupBox_8->setTitle(QString());
        label_26->setText(QCoreApplication::translate("FormCombinedDev", "\346\250\241\345\274\217\351\200\211\346\213\251\357\274\232", nullptr));
        btnSet_6->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_17->setTitle(QString());
        label_37->setText(QCoreApplication::translate("FormCombinedDev", "\344\275\216\345\231\252\346\224\276\345\267\245\344\275\234\346\250\241\345\274\217\357\274\232", nullptr));
        btnSet_18->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_9->setTitle(QString());
        label_27->setText(QCoreApplication::translate("FormCombinedDev", "\344\275\216\345\231\252\346\224\276\350\276\223\345\207\272\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        ctrl_hpa_output_power_2->setText(QCoreApplication::translate("FormCombinedDev", "-5", nullptr));
        btnSet_7->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_19->setTitle(QString());
        label_36->setText(QCoreApplication::translate("FormCombinedDev", "\344\275\216\345\231\252\346\224\276\346\263\265\346\265\246\347\224\265\346\265\201(mA)\357\274\232", nullptr));
        ctrl_current_2->setText(QCoreApplication::translate("FormCombinedDev", "80", nullptr));
        btnSet_17->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_10->setTitle(QString());
        label_29->setText(QCoreApplication::translate("FormCombinedDev", "\346\277\200\345\205\211\346\263\242\346\256\265(C13-C61)\357\274\232", nullptr));
        btnSet_8->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_11->setTitle(QString());
        label_30->setText(QCoreApplication::translate("FormCombinedDev", "\345\233\272\345\256\232\345\201\217\345\267\256(MHz)\357\274\232", nullptr));
        ctrl_freq_compensate_2->setText(QCoreApplication::translate("FormCombinedDev", "0", nullptr));
        btnSet_9->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_22->setTitle(QString());
        label_40->setText(QCoreApplication::translate("FormCombinedDev", "\346\216\245\346\224\266\346\263\242\351\225\277(nm)\357\274\232", nullptr));
        ctrl_wave_compensate_2->setText(QCoreApplication::translate("FormCombinedDev", "0", nullptr));
        btnSet_21->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_24->setTitle(QString());
        label_46->setText(QCoreApplication::translate("FormCombinedDev", "\346\216\245\346\224\266\351\242\221\347\216\207(GHz)\357\274\232", nullptr));
        ctrl_freq_2->setText(QCoreApplication::translate("FormCombinedDev", "0", nullptr));
        btnSet_25->setText(QCoreApplication::translate("FormCombinedDev", "\350\256\276\347\275\256", nullptr));
        groupBox_13->setTitle(QString());
        sta_recv_prbs->setText(QString());
        btnSet_14->setText(QCoreApplication::translate("FormCombinedDev", "\346\211\223\345\274\200\347\272\277\350\267\257\344\276\247PRBS\346\224\266\346\225\260", nullptr));
        btnSet_15->setText(QCoreApplication::translate("FormCombinedDev", "\345\205\263\351\227\255\347\272\277\350\267\257\344\276\247PRBS\346\224\266\346\225\260", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FormCombinedDev: public Ui_FormCombinedDev {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FORMCOMBINEDDEV_H
