/********************************************************************************
** Form generated from reading UI file 'formopticalmodule.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FORMOPTICALMODULE_H
#define UI_FORMOPTICALMODULE_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_FormOpticalModule
{
public:
    QVBoxLayout *verticalLayout_2;
    QGroupBox *groupBox_5;
    QVBoxLayout *verticalLayout_4;
    QHBoxLayout *horizontalLayout;
    QLabel *label_2;
    QLabel *sta_power_status;
    QSpacerItem *horizontalSpacer_7;
    QLabel *label_5;
    QLabel *sta_switch_status;
    QSpacerItem *horizontalSpacer_6;
    QLabel *label_19;
    QLabel *sta_config_status;
    QSpacerItem *horizontalSpacer_8;
    QLabel *label_21;
    QLabel *sta_amplifier_status;
    QGridLayout *gridLayout;
    QLabel *label_10;
    QSpacerItem *horizontalSpacer_3;
    QLabel *snr;
    QLabel *output_power;
    QLabel *label_3;
    QLabel *label_18;
    QLabel *label_6;
    QLabel *sent_power;
    QLabel *freq_offset;
    QLabel *input_power;
    QSpacerItem *horizontalSpacer;
    QLabel *label_16;
    QLabel *bit_count;
    QLabel *label_8;
    QSpacerItem *horizontalSpacer_2;
    QGridLayout *gridLayout_6;
    QGroupBox *groupBox;
    QHBoxLayout *horizontalLayout_2;
    QGridLayout *gridLayout_2;
    QLabel *label_11;
    QComboBox *ctrl_module_status;
    QPushButton *btnSet_b010;
    QSpacerItem *horizontalSpacer_10;
    QGroupBox *groupBox_2;
    QHBoxLayout *horizontalLayout_3;
    QGridLayout *gridLayout_3;
    QLabel *label_12;
    QPushButton *btnSet_b410;
    QLineEdit *ctrl_output_power;
    QSpacerItem *horizontalSpacer_11;
    QGroupBox *groupBox_3;
    QHBoxLayout *horizontalLayout_4;
    QGridLayout *gridLayout_4;
    QLineEdit *ctrl_channel_num;
    QLabel *label_13;
    QComboBox *ctrl_freq_interval;
    QPushButton *btnSet_b400;
    QLabel *label_14;
    QGroupBox *groupBox_4;
    QVBoxLayout *verticalLayout;
    QGridLayout *gridLayout_5;
    QLabel *label_23;
    QPushButton *btnSet_b430;
    QLineEdit *ctrl_freq_compensate;
    QSpacerItem *horizontalSpacer_12;
    QGroupBox *groupBox_6;
    QVBoxLayout *verticalLayout_3;
    QHBoxLayout *horizontalLayout_6;
    QLabel *label_17;
    QLineEdit *control_command;
    QSpacerItem *horizontalSpacer_14;
    QPushButton *btnTestCommand;
    QHBoxLayout *horizontalLayout_8;
    QSpacerItem *horizontalSpacer_4;
    QPushButton *btnRecord;
    QPushButton *btnRecord_2;
    QGroupBox *groupBox_7;
    QHBoxLayout *horizontalLayout_7;
    QHBoxLayout *horizontalLayout_5;
    QLabel *label_15;
    QLineEdit *control_result;

    void setupUi(QWidget *FormOpticalModule)
    {
        if (FormOpticalModule->objectName().isEmpty())
            FormOpticalModule->setObjectName(QString::fromUtf8("FormOpticalModule"));
        FormOpticalModule->resize(1020, 716);
        verticalLayout_2 = new QVBoxLayout(FormOpticalModule);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        groupBox_5 = new QGroupBox(FormOpticalModule);
        groupBox_5->setObjectName(QString::fromUtf8("groupBox_5"));
        QFont font;
        font.setPointSize(16);
        groupBox_5->setFont(font);
        verticalLayout_4 = new QVBoxLayout(groupBox_5);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        label_2 = new QLabel(groupBox_5);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        QFont font1;
        font1.setPointSize(14);
        label_2->setFont(font1);

        horizontalLayout->addWidget(label_2);

        sta_power_status = new QLabel(groupBox_5);
        sta_power_status->setObjectName(QString::fromUtf8("sta_power_status"));
        sta_power_status->setMinimumSize(QSize(32, 32));
        sta_power_status->setMaximumSize(QSize(32, 32));
        sta_power_status->setFont(font1);
        sta_power_status->setStyleSheet(QString::fromUtf8("background-image: url(:/image/image/Gray_Round32.bmp);"));

        horizontalLayout->addWidget(sta_power_status);

        horizontalSpacer_7 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_7);

        label_5 = new QLabel(groupBox_5);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setFont(font1);

        horizontalLayout->addWidget(label_5);

        sta_switch_status = new QLabel(groupBox_5);
        sta_switch_status->setObjectName(QString::fromUtf8("sta_switch_status"));
        sta_switch_status->setMinimumSize(QSize(32, 32));
        sta_switch_status->setMaximumSize(QSize(32, 32));
        sta_switch_status->setFont(font1);
        sta_switch_status->setStyleSheet(QString::fromUtf8("background-image: url(:/image/image/Gray_Round32.bmp);"));

        horizontalLayout->addWidget(sta_switch_status);

        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_6);

        label_19 = new QLabel(groupBox_5);
        label_19->setObjectName(QString::fromUtf8("label_19"));
        label_19->setFont(font1);

        horizontalLayout->addWidget(label_19);

        sta_config_status = new QLabel(groupBox_5);
        sta_config_status->setObjectName(QString::fromUtf8("sta_config_status"));
        sta_config_status->setMinimumSize(QSize(32, 32));
        sta_config_status->setMaximumSize(QSize(32, 32));
        sta_config_status->setFont(font1);
        sta_config_status->setStyleSheet(QString::fromUtf8("background-image: url(:/image/image/Gray_Round32.bmp);"));

        horizontalLayout->addWidget(sta_config_status);

        horizontalSpacer_8 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_8);

        label_21 = new QLabel(groupBox_5);
        label_21->setObjectName(QString::fromUtf8("label_21"));
        label_21->setFont(font1);

        horizontalLayout->addWidget(label_21);

        sta_amplifier_status = new QLabel(groupBox_5);
        sta_amplifier_status->setObjectName(QString::fromUtf8("sta_amplifier_status"));
        sta_amplifier_status->setMinimumSize(QSize(32, 32));
        sta_amplifier_status->setMaximumSize(QSize(32, 32));
        sta_amplifier_status->setFont(font1);
        sta_amplifier_status->setStyleSheet(QString::fromUtf8("background-image: url(:/image/image/Gray_Round32.bmp);"));

        horizontalLayout->addWidget(sta_amplifier_status);

        horizontalLayout->setStretch(0, 2);
        horizontalLayout->setStretch(1, 1);
        horizontalLayout->setStretch(3, 2);
        horizontalLayout->setStretch(4, 1);
        horizontalLayout->setStretch(6, 2);
        horizontalLayout->setStretch(7, 1);
        horizontalLayout->setStretch(9, 2);
        horizontalLayout->setStretch(10, 1);

        verticalLayout_4->addLayout(horizontalLayout);

        gridLayout = new QGridLayout();
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        label_10 = new QLabel(groupBox_5);
        label_10->setObjectName(QString::fromUtf8("label_10"));
        label_10->setFont(font1);

        gridLayout->addWidget(label_10, 2, 0, 1, 1);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer_3, 2, 2, 1, 1);

        snr = new QLabel(groupBox_5);
        snr->setObjectName(QString::fromUtf8("snr"));
        snr->setMaximumSize(QSize(16777215, 40));
        snr->setFont(font1);
        snr->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout->addWidget(snr, 1, 1, 1, 1);

        output_power = new QLabel(groupBox_5);
        output_power->setObjectName(QString::fromUtf8("output_power"));
        output_power->setMaximumSize(QSize(16777215, 40));
        output_power->setFont(font1);
        output_power->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout->addWidget(output_power, 0, 4, 1, 1);

        label_3 = new QLabel(groupBox_5);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        label_3->setFont(font1);

        gridLayout->addWidget(label_3, 0, 3, 1, 1);

        label_18 = new QLabel(groupBox_5);
        label_18->setObjectName(QString::fromUtf8("label_18"));
        label_18->setFont(font1);

        gridLayout->addWidget(label_18, 2, 3, 1, 1);

        label_6 = new QLabel(groupBox_5);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        label_6->setFont(font1);

        gridLayout->addWidget(label_6, 0, 0, 1, 1);

        sent_power = new QLabel(groupBox_5);
        sent_power->setObjectName(QString::fromUtf8("sent_power"));
        sent_power->setMinimumSize(QSize(180, 0));
        sent_power->setMaximumSize(QSize(16777215, 40));
        sent_power->setFont(font1);
        sent_power->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout->addWidget(sent_power, 1, 4, 1, 1);

        freq_offset = new QLabel(groupBox_5);
        freq_offset->setObjectName(QString::fromUtf8("freq_offset"));
        freq_offset->setMaximumSize(QSize(16777215, 40));
        freq_offset->setFont(font1);
        freq_offset->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout->addWidget(freq_offset, 2, 1, 1, 1);

        input_power = new QLabel(groupBox_5);
        input_power->setObjectName(QString::fromUtf8("input_power"));
        input_power->setMaximumSize(QSize(16777215, 40));
        input_power->setFont(font1);
        input_power->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout->addWidget(input_power, 0, 1, 1, 1);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer, 0, 2, 1, 1);

        label_16 = new QLabel(groupBox_5);
        label_16->setObjectName(QString::fromUtf8("label_16"));
        label_16->setFont(font1);

        gridLayout->addWidget(label_16, 1, 3, 1, 1);

        bit_count = new QLabel(groupBox_5);
        bit_count->setObjectName(QString::fromUtf8("bit_count"));
        bit_count->setMaximumSize(QSize(16777215, 40));
        bit_count->setFont(font1);
        bit_count->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));

        gridLayout->addWidget(bit_count, 2, 4, 1, 1);

        label_8 = new QLabel(groupBox_5);
        label_8->setObjectName(QString::fromUtf8("label_8"));
        label_8->setFont(font1);

        gridLayout->addWidget(label_8, 1, 0, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer_2, 1, 2, 1, 1);

        gridLayout->setColumnStretch(0, 1);
        gridLayout->setColumnStretch(1, 1);
        gridLayout->setColumnStretch(3, 1);
        gridLayout->setColumnStretch(4, 1);

        verticalLayout_4->addLayout(gridLayout);

        verticalLayout_4->setStretch(1, 1);

        verticalLayout_2->addWidget(groupBox_5);

        gridLayout_6 = new QGridLayout();
        gridLayout_6->setObjectName(QString::fromUtf8("gridLayout_6"));
        groupBox = new QGroupBox(FormOpticalModule);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        groupBox->setFont(font);
        horizontalLayout_2 = new QHBoxLayout(groupBox);
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        gridLayout_2 = new QGridLayout();
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        label_11 = new QLabel(groupBox);
        label_11->setObjectName(QString::fromUtf8("label_11"));
        label_11->setFont(font1);

        gridLayout_2->addWidget(label_11, 0, 0, 1, 1);

        ctrl_module_status = new QComboBox(groupBox);
        ctrl_module_status->setObjectName(QString::fromUtf8("ctrl_module_status"));
        ctrl_module_status->setMinimumSize(QSize(0, 36));
        ctrl_module_status->setMaximumSize(QSize(160, 16777215));

        gridLayout_2->addWidget(ctrl_module_status, 0, 1, 1, 1);

        btnSet_b010 = new QPushButton(groupBox);
        btnSet_b010->setObjectName(QString::fromUtf8("btnSet_b010"));
        btnSet_b010->setMinimumSize(QSize(140, 40));
        btnSet_b010->setFont(font1);

        gridLayout_2->addWidget(btnSet_b010, 0, 3, 1, 1);

        horizontalSpacer_10 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_2->addItem(horizontalSpacer_10, 0, 2, 1, 1);

        gridLayout_2->setColumnStretch(0, 1);
        gridLayout_2->setColumnStretch(1, 1);
        gridLayout_2->setColumnStretch(3, 1);

        horizontalLayout_2->addLayout(gridLayout_2);


        gridLayout_6->addWidget(groupBox, 0, 0, 1, 1);

        groupBox_2 = new QGroupBox(FormOpticalModule);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        groupBox_2->setFont(font);
        horizontalLayout_3 = new QHBoxLayout(groupBox_2);
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        gridLayout_3 = new QGridLayout();
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        label_12 = new QLabel(groupBox_2);
        label_12->setObjectName(QString::fromUtf8("label_12"));
        label_12->setFont(font1);

        gridLayout_3->addWidget(label_12, 0, 0, 1, 1);

        btnSet_b410 = new QPushButton(groupBox_2);
        btnSet_b410->setObjectName(QString::fromUtf8("btnSet_b410"));
        btnSet_b410->setMinimumSize(QSize(140, 40));
        btnSet_b410->setFont(font1);

        gridLayout_3->addWidget(btnSet_b410, 0, 3, 1, 1);

        ctrl_output_power = new QLineEdit(groupBox_2);
        ctrl_output_power->setObjectName(QString::fromUtf8("ctrl_output_power"));
        ctrl_output_power->setMinimumSize(QSize(0, 40));
        ctrl_output_power->setMaximumSize(QSize(160, 16777215));
        ctrl_output_power->setFont(font);

        gridLayout_3->addWidget(ctrl_output_power, 0, 1, 1, 1);

        horizontalSpacer_11 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_3->addItem(horizontalSpacer_11, 0, 2, 1, 1);

        gridLayout_3->setColumnStretch(0, 1);
        gridLayout_3->setColumnStretch(1, 1);
        gridLayout_3->setColumnStretch(3, 1);

        horizontalLayout_3->addLayout(gridLayout_3);


        gridLayout_6->addWidget(groupBox_2, 0, 1, 1, 1);

        groupBox_3 = new QGroupBox(FormOpticalModule);
        groupBox_3->setObjectName(QString::fromUtf8("groupBox_3"));
        groupBox_3->setFont(font);
        horizontalLayout_4 = new QHBoxLayout(groupBox_3);
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        gridLayout_4 = new QGridLayout();
        gridLayout_4->setObjectName(QString::fromUtf8("gridLayout_4"));
        ctrl_channel_num = new QLineEdit(groupBox_3);
        ctrl_channel_num->setObjectName(QString::fromUtf8("ctrl_channel_num"));
        ctrl_channel_num->setMinimumSize(QSize(0, 40));
        ctrl_channel_num->setFont(font);

        gridLayout_4->addWidget(ctrl_channel_num, 0, 3, 1, 1);

        label_13 = new QLabel(groupBox_3);
        label_13->setObjectName(QString::fromUtf8("label_13"));
        label_13->setFont(font1);

        gridLayout_4->addWidget(label_13, 0, 0, 1, 1);

        ctrl_freq_interval = new QComboBox(groupBox_3);
        ctrl_freq_interval->setObjectName(QString::fromUtf8("ctrl_freq_interval"));
        ctrl_freq_interval->setMinimumSize(QSize(0, 36));
        ctrl_freq_interval->setFont(font);

        gridLayout_4->addWidget(ctrl_freq_interval, 0, 1, 1, 1);

        btnSet_b400 = new QPushButton(groupBox_3);
        btnSet_b400->setObjectName(QString::fromUtf8("btnSet_b400"));
        btnSet_b400->setMinimumSize(QSize(140, 40));
        btnSet_b400->setFont(font1);

        gridLayout_4->addWidget(btnSet_b400, 0, 4, 1, 1);

        label_14 = new QLabel(groupBox_3);
        label_14->setObjectName(QString::fromUtf8("label_14"));
        label_14->setFont(font1);

        gridLayout_4->addWidget(label_14, 0, 2, 1, 1);

        gridLayout_4->setColumnStretch(0, 1);
        gridLayout_4->setColumnStretch(1, 1);
        gridLayout_4->setColumnStretch(2, 1);
        gridLayout_4->setColumnStretch(3, 1);
        gridLayout_4->setColumnStretch(4, 1);

        horizontalLayout_4->addLayout(gridLayout_4);


        gridLayout_6->addWidget(groupBox_3, 1, 0, 1, 1);

        groupBox_4 = new QGroupBox(FormOpticalModule);
        groupBox_4->setObjectName(QString::fromUtf8("groupBox_4"));
        groupBox_4->setFont(font);
        verticalLayout = new QVBoxLayout(groupBox_4);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        gridLayout_5 = new QGridLayout();
        gridLayout_5->setObjectName(QString::fromUtf8("gridLayout_5"));
        label_23 = new QLabel(groupBox_4);
        label_23->setObjectName(QString::fromUtf8("label_23"));
        label_23->setFont(font1);

        gridLayout_5->addWidget(label_23, 0, 0, 1, 1);

        btnSet_b430 = new QPushButton(groupBox_4);
        btnSet_b430->setObjectName(QString::fromUtf8("btnSet_b430"));
        btnSet_b430->setMinimumSize(QSize(140, 40));
        btnSet_b430->setFont(font1);

        gridLayout_5->addWidget(btnSet_b430, 0, 3, 1, 1);

        ctrl_freq_compensate = new QLineEdit(groupBox_4);
        ctrl_freq_compensate->setObjectName(QString::fromUtf8("ctrl_freq_compensate"));
        ctrl_freq_compensate->setMinimumSize(QSize(0, 40));
        ctrl_freq_compensate->setMaximumSize(QSize(160, 16777215));
        ctrl_freq_compensate->setFont(font);

        gridLayout_5->addWidget(ctrl_freq_compensate, 0, 1, 1, 1);

        horizontalSpacer_12 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_5->addItem(horizontalSpacer_12, 0, 2, 1, 1);

        gridLayout_5->setColumnStretch(0, 1);
        gridLayout_5->setColumnStretch(1, 1);
        gridLayout_5->setColumnStretch(3, 1);

        verticalLayout->addLayout(gridLayout_5);


        gridLayout_6->addWidget(groupBox_4, 1, 1, 1, 1);

        gridLayout_6->setColumnStretch(0, 1);
        gridLayout_6->setColumnStretch(1, 1);

        verticalLayout_2->addLayout(gridLayout_6);

        groupBox_6 = new QGroupBox(FormOpticalModule);
        groupBox_6->setObjectName(QString::fromUtf8("groupBox_6"));
        groupBox_6->setFont(font);
        verticalLayout_3 = new QVBoxLayout(groupBox_6);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        label_17 = new QLabel(groupBox_6);
        label_17->setObjectName(QString::fromUtf8("label_17"));
        label_17->setFont(font1);

        horizontalLayout_6->addWidget(label_17);

        control_command = new QLineEdit(groupBox_6);
        control_command->setObjectName(QString::fromUtf8("control_command"));
        control_command->setMinimumSize(QSize(0, 40));

        horizontalLayout_6->addWidget(control_command);

        horizontalSpacer_14 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_14);

        btnTestCommand = new QPushButton(groupBox_6);
        btnTestCommand->setObjectName(QString::fromUtf8("btnTestCommand"));
        btnTestCommand->setMinimumSize(QSize(140, 40));
        btnTestCommand->setFont(font1);

        horizontalLayout_6->addWidget(btnTestCommand);


        verticalLayout_3->addLayout(horizontalLayout_6);

        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setObjectName(QString::fromUtf8("horizontalLayout_8"));
        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_8->addItem(horizontalSpacer_4);

        btnRecord = new QPushButton(groupBox_6);
        btnRecord->setObjectName(QString::fromUtf8("btnRecord"));
        btnRecord->setMinimumSize(QSize(140, 40));
        btnRecord->setFont(font1);
        btnRecord->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_8->addWidget(btnRecord);

        btnRecord_2 = new QPushButton(groupBox_6);
        btnRecord_2->setObjectName(QString::fromUtf8("btnRecord_2"));
        btnRecord_2->setMinimumSize(QSize(140, 40));
        btnRecord_2->setFont(font1);
        btnRecord_2->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_8->addWidget(btnRecord_2);


        verticalLayout_3->addLayout(horizontalLayout_8);


        verticalLayout_2->addWidget(groupBox_6);

        groupBox_7 = new QGroupBox(FormOpticalModule);
        groupBox_7->setObjectName(QString::fromUtf8("groupBox_7"));
        groupBox_7->setFont(font);
        horizontalLayout_7 = new QHBoxLayout(groupBox_7);
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        label_15 = new QLabel(groupBox_7);
        label_15->setObjectName(QString::fromUtf8("label_15"));
        label_15->setFont(font1);

        horizontalLayout_5->addWidget(label_15);

        control_result = new QLineEdit(groupBox_7);
        control_result->setObjectName(QString::fromUtf8("control_result"));
        control_result->setMinimumSize(QSize(0, 40));

        horizontalLayout_5->addWidget(control_result);


        horizontalLayout_7->addLayout(horizontalLayout_5);


        verticalLayout_2->addWidget(groupBox_7);

        verticalLayout_2->setStretch(0, 1);
        verticalLayout_2->setStretch(1, 1);

        retranslateUi(FormOpticalModule);

        QMetaObject::connectSlotsByName(FormOpticalModule);
    } // setupUi

    void retranslateUi(QWidget *FormOpticalModule)
    {
        FormOpticalModule->setWindowTitle(QCoreApplication::translate("FormOpticalModule", "Form", nullptr));
        groupBox_5->setTitle(QCoreApplication::translate("FormOpticalModule", "\347\212\266\346\200\201\345\217\202\346\225\260", nullptr));
        label_2->setText(QCoreApplication::translate("FormOpticalModule", "\345\212\237\350\200\227\347\212\266\346\200\201\357\274\232", nullptr));
        sta_power_status->setText(QString());
        label_5->setText(QCoreApplication::translate("FormOpticalModule", "\344\274\240\350\276\223\351\200\232\351\201\223\347\212\266\346\200\201\357\274\232", nullptr));
        sta_switch_status->setText(QString());
        label_19->setText(QCoreApplication::translate("FormOpticalModule", "\344\274\240\350\276\223\351\205\215\347\275\256\347\212\266\346\200\201\357\274\232", nullptr));
        sta_config_status->setText(QString());
        label_21->setText(QCoreApplication::translate("FormOpticalModule", "\345\212\237\347\216\207\346\224\276\345\244\247\345\231\250\345\267\245\344\275\234\347\212\266\346\200\201\357\274\232", nullptr));
        sta_amplifier_status->setText(QString());
        label_10->setText(QCoreApplication::translate("FormOpticalModule", "\351\242\221\345\201\217\344\274\260\350\256\241\345\200\274(MHz)\357\274\232", nullptr));
        snr->setText(QCoreApplication::translate("FormOpticalModule", "TextLabel", nullptr));
        output_power->setText(QCoreApplication::translate("FormOpticalModule", "TextLabel", nullptr));
        label_3->setText(QCoreApplication::translate("FormOpticalModule", "\350\276\223\345\207\272\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        label_18->setText(QCoreApplication::translate("FormOpticalModule", "\350\257\257\347\240\201\347\216\207\347\273\237\350\256\241\357\274\232", nullptr));
        label_6->setText(QCoreApplication::translate("FormOpticalModule", "\350\276\223\345\205\245\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        sent_power->setText(QCoreApplication::translate("FormOpticalModule", "TextLabel", nullptr));
        freq_offset->setText(QCoreApplication::translate("FormOpticalModule", "TextLabel", nullptr));
        input_power->setText(QCoreApplication::translate("FormOpticalModule", "TextLabel", nullptr));
        label_16->setText(QCoreApplication::translate("FormOpticalModule", "\350\275\275\346\263\242\351\242\221\347\216\207(GHz)\357\274\232", nullptr));
        bit_count->setText(QCoreApplication::translate("FormOpticalModule", "TextLabel", nullptr));
        label_8->setText(QCoreApplication::translate("FormOpticalModule", "\346\216\245\346\224\266\345\205\211\344\277\241\345\231\252\346\257\224(dB)\357\274\232", nullptr));
        groupBox->setTitle(QCoreApplication::translate("FormOpticalModule", "\347\212\266\346\200\201\346\216\247\345\210\266", nullptr));
        label_11->setText(QCoreApplication::translate("FormOpticalModule", "\347\212\266\346\200\201\357\274\232", nullptr));
        btnSet_b010->setText(QCoreApplication::translate("FormOpticalModule", "\350\256\276\347\275\256", nullptr));
        groupBox_2->setTitle(QCoreApplication::translate("FormOpticalModule", "\350\276\223\345\207\272\345\212\237\347\216\207\346\216\247\345\210\266", nullptr));
        label_12->setText(QCoreApplication::translate("FormOpticalModule", "\350\276\223\345\207\272\345\212\237\347\216\207(dBm)\357\274\232", nullptr));
        btnSet_b410->setText(QCoreApplication::translate("FormOpticalModule", "\350\256\276\347\275\256", nullptr));
        ctrl_output_power->setText(QCoreApplication::translate("FormOpticalModule", "0", nullptr));
        groupBox_3->setTitle(QCoreApplication::translate("FormOpticalModule", "\350\275\275\346\263\242\351\242\221\347\216\207\346\216\247\345\210\266", nullptr));
        ctrl_channel_num->setText(QCoreApplication::translate("FormOpticalModule", "18", nullptr));
        label_13->setText(QCoreApplication::translate("FormOpticalModule", "\351\242\221\347\216\207\351\227\264\351\232\224\357\274\232", nullptr));
        btnSet_b400->setText(QCoreApplication::translate("FormOpticalModule", "\350\256\276\347\275\256", nullptr));
        label_14->setText(QCoreApplication::translate("FormOpticalModule", "\351\200\232\351\201\223\344\270\252\346\225\260\357\274\232", nullptr));
        groupBox_4->setTitle(QCoreApplication::translate("FormOpticalModule", "\351\242\221\347\216\207\351\242\204\350\241\245\345\201\277\346\216\247\345\210\266", nullptr));
        label_23->setText(QCoreApplication::translate("FormOpticalModule", "\351\242\221\347\216\207\350\241\245\345\201\277(MHz)\357\274\232", nullptr));
        btnSet_b430->setText(QCoreApplication::translate("FormOpticalModule", "\350\256\276\347\275\256", nullptr));
        ctrl_freq_compensate->setText(QCoreApplication::translate("FormOpticalModule", "0", nullptr));
        groupBox_6->setTitle(QCoreApplication::translate("FormOpticalModule", "\346\216\247\345\210\266\345\217\260", nullptr));
        label_17->setText(QCoreApplication::translate("FormOpticalModule", "\346\211\247\350\241\214\345\221\275\344\273\244\357\274\232", nullptr));
        control_command->setText(QCoreApplication::translate("FormOpticalModule", "1111", nullptr));
        btnTestCommand->setText(QCoreApplication::translate("FormOpticalModule", "\350\256\276\347\275\256", nullptr));
        btnRecord->setText(QCoreApplication::translate("FormOpticalModule", "\350\256\260\345\275\225\347\212\266\346\200\201", nullptr));
        btnRecord_2->setText(QCoreApplication::translate("FormOpticalModule", "\346\211\223\345\274\200\347\212\266\346\200\201\350\241\250", nullptr));
        groupBox_7->setTitle(QCoreApplication::translate("FormOpticalModule", "\346\211\247\350\241\214\347\273\223\346\236\234", nullptr));
        label_15->setText(QCoreApplication::translate("FormOpticalModule", "\346\211\247\350\241\214\347\273\223\346\236\234\357\274\232", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FormOpticalModule: public Ui_FormOpticalModule {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FORMOPTICALMODULE_H
