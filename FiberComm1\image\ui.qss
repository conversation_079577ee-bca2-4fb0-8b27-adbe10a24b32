﻿/*主窗口背景颜色*/
QWidget#FormCombinedDev{
	/*background-color: qlineargradient(spread:pad, x1:0, y1:1, x2:1, y2:0, stop:0 rgba(240, 240, 240, 255), stop:1 rgba(220, 235, 245, 255));*//*绿白渐变*/	
	/*background-color: rgb(4, 20, 54);*//*黑蓝色*/
	background-color: qlineargradient(spread:pad, x1:0.294, y1:0, x2:0.751, y2:1, stop:0 rgba(8, 30, 46, 255), stop:1 rgba(11, 74, 116, 255));/*蓝色渐变*/
}





/******************************************************************************************************************/




/*QMessageBox样式表*/
QMessageBox {
        background-color: qlineargradient(spread:pad, x1:0.294, y1:0, x2:0.751, y2:1, stop:0 rgba(8, 30, 46, 255), stop:1 rgba(11, 74, 116, 255));
        border: 2px solid #34495e;
        color: #ffffff;
	font: 22px "Arial"; 
  }
 QMessageBox QLabel {
        /*background-color: #34495e;*/
        color: #ffffff;
        border: none;
        padding: 10px;
}
QMessageBox QPushButton {
	width:100px;
        border-radius:8px;
	border:1px solid #abc7e5;
	padding:0;
	margin:0;
	color:#000000;
	font: 18px "Arial";
	 background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(100, 160, 200, 255), stop:1 rgba(80, 140, 180, 255)); /* 浅蓝色渐变 */
}
QMessageBox QPushButton:hover {
         background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(120, 180, 220, 255), stop:1 rgba(100, 160, 200, 255)); /* 悬停时稍亮 */

}
/******************************************************************************************************************/

QLabel[showLabel = "attrbiute"]{
	color:#ffffff;
}
QLabel[textLabel = "params"]{
	border-radius:8px;
	/*padding-left:10px;*/
	padding-left:20px;
	font: 22px "Arial"; /* Times New Roman、Arial、Courier New */
	background-color: rgb(145, 177, 188);
	color: rgb(255, 255, 255);
}
QLabel[lampLabel= "png"]{
	
}


/******************************************************************************************************************/


QLineEdit[LineEdit = "edit"]{
	color:#000000;
	border:1px solid #abc7e5;
	background-color: rgba(200, 230, 235, 0.7);
	border-radius:8px;
	/*padding-left:10px;*/
	padding-left:20px;
	font: 22px "Arial"; /* Times New Roman、Arial、Courier New */
	/*border:1px solid rgba(20, 214, 92);*/
	selection-background-color: #0a4064;/*选中文本的背景颜色*/
}
QLineEdit[LineEdit = "edit"]:focus {
    background-color: rgba(180, 220, 225, 1); /* 聚焦时的输入框背景颜色 */
    border: 1px solid rgba(40, 140, 150, 1); /* 聚焦时的输入框边框颜色 */
}
QLineEdit[LineEdit = "edit"]:hover{
	/*background-color: qlineargradient(spread:repeat, x1:0.672, y1:0.852636, x2:0, y2:0.483, stop:0 rgba(20, 213, 94, 255), stop:1 rgba(29, 217, 96, 255));*/
	background-color: rgb(240, 240, 240);
}

/******************************************************************************************************************/


/*下拉框*/
QComboBox{
	width:100px;
	border-radius:8px;
	border:1px solid #abc7e5;
	padding:0;
	margin:0;
	color:#000000;
	font: 18px "Arial";
	padding-left:20px;
	background-color: rgba(200, 230, 235, 0.7);
	selection-background-color: #0a4064;/*选中文本的背景颜色*/
}
/*下拉框选项*/
QComboBox QAbstractItemView{
	/*outline:2px solid qlineargradient(spread:pad, x1:0.483146, y1:1, x2:0.461, y2:0, stop:0 rgba(181, 194, 212, 255), stop:1 rgba(96, 125, 166, 255));*/
	outline:3px solid rgba(145, 177, 188);
	/*alignment:AlignCenter;;/*选项的边框*/
	border-radius:8px;
	selection-background-color:rgba(255,255,255);/*选项的背景*/
	selection-color:rgba(0, 0, 0);/*选项字体颜色*/
	background-color:rgba(255,255,255);/*下拉框背景颜色*/
	
	padding-left:2px;
	padding-right:2px;
	font: 18px "Arial";
}
/* 下拉框滚动条的样式 */
QComboBox QAbstractItemView QScrollBar:vertical{
	width:12px;
	background-color: rgb(255, 255, 255);
	border-radius:5px;
}
QComboBox QAbstractItemView QScrollBar::handle:vertical {
    	background: rgb(202, 209, 217); /* 滚动条滑块颜色 */
    	min-height: 20px; /* 滑块最小高度 */
	border-radius:6px;
}
QComboBox QAbstractItemView::item{
}
QComboBox QAbstractItemView::item:hover{
}
QComboBox QAbstractItemView::item:selected{
}
/*下拉前箭头按钮*/
QComboBox::drop-down{
	subcontrol-origin: padding;
    	subcontrol-position: right;
	height:20px;
	width:20px;
	border-radius:10px;
	border:none;
	background-color: transparent; /* 设置为透明 */
	image:url(:/icon/image/arrow_left.png);
}
QComboBox::down-arrow{
	width: 20px;
    	height: 20px;
}
/* 点击后的箭头效果 */
QComboBox::drop-down:checked {
	image:url(:/icon/image/arrow_down.png);
	border-left:1px solid rgba(0,0,0,0.2);
	border-top:1px solid rgba(0,0,0,0.2);
	border-right:1px solid rgba(0,0,0,0.2);
	border-bottom:1px solid rgba(0,0,0,0.2);
}



/******************************************************************************************************************/
/*所有的设置按钮*/
QPushButton:disabled {
   	background-color: rgba(140, 140, 140); /* 暗灰色背景，半透明 */
    	color: rgba(0, 0, 0); /* 浅灰色文字，半透明 */
}

QPushButton[settingBtn = "setBtn"]{
	width:140px;
	border-radius:8px;
	border:1px solid #abc7e5;
	padding:0;
	margin:0;
	color:#000000;
	font: 18px "Arial";
	 background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(100, 160, 200, 255), stop:1 rgba(80, 140, 180, 255)); /* 浅蓝色渐变 */
}QPushButton[settingBtn = "setBtn"]:hover{
	 background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(120, 180, 220, 255), stop:1 rgba(100, 160, 200, 255)); /* 悬停时稍亮 */
}QPushButton[settingBtn = "setBtn"]::pressed{
	border-left:2px solid rgba(240, 240, 240);
	border-top:2px solid rgba(240, 240, 240);
	border-right:2px solid rgba(0,0,0,0.6);
	border-bottom:2px solid rgba(0,0,0,0.6);
	 background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(80, 140, 180, 255), stop:1 rgba(60, 120, 160, 255)); /* 按下时稍深 */
}
/*发送端*/
QPushButton#btnSet_10:hover{
	 background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(255, 180, 90, 255), stop:1 rgba(240, 160, 70, 255)); /* 悬停时稍亮 */
}QPushButton#btnSet_10::pressed{
	border-left:2px solid rgba(240, 240, 240);
	border-top:2px solid rgba(240, 240, 240);
	border-right:2px solid rgba(0,0,0,0.6);
	border-bottom:2px solid rgba(0,0,0,0.6);
	 background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(80, 140, 180, 255), stop:1 rgba(60, 120, 160, 255)); /* 按下时稍深 */
}QPushButton#btnSet_12:hover{
	background-color: rgba(255, 131, 131);
}QPushButton#btnSet_12::pressed{
	border-left:2px solid rgba(240, 240, 240);
	border-top:2px solid rgba(240, 240, 240);
	border-right:2px solid rgba(0,0,0,0.6);
	border-bottom:2px solid rgba(0,0,0,0.6);
	 background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(80, 140, 180, 255), stop:1 rgba(60, 120, 160, 255)); /* 按下时稍深 */
}
/*接收端*/
QPushButton#btnSet_14:hover{
	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(255, 180, 90, 255), stop:1 rgba(240, 160, 70, 255)); /* 悬停时稍亮 */
	/*background-color: rgba(64, 158, 255);*/
}QPushButton#btnSet_14::pressed{
	border-left:2px solid rgba(240, 240, 240);
	border-top:2px solid rgba(240, 240, 240);
	border-right:2px solid rgba(0,0,0,0.6);
	border-bottom:2px solid rgba(0,0,0,0.6);
	 background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(80, 140, 180, 255), stop:1 rgba(60, 120, 160, 255)); /* 按下时稍深 */
}
QPushButton#btnSet_15:hover{
	background-color: rgba(255, 131, 131);
}QPushButton#btnSet_15::pressed{
	border-left:2px solid rgba(240, 240, 240);
	border-top:2px solid rgba(240, 240, 240);
	border-right:2px solid rgba(0,0,0,0.6);
	border-bottom:2px solid rgba(0,0,0,0.6);
	 background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(80, 140, 180, 255), stop:1 rgba(60, 120, 160, 255)); /* 按下时稍深 */
}

QPushButton[sendBtn = "openData"]{
	width:140px;
	border-radius:8px;
	border:1px solid #abc7e5;
	padding:0;
	margin:0;
	color:#000000;
	font: 18px "Arial";
	 background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(100, 160, 200, 255), stop:1 rgba(80, 140, 180, 255)); /* 浅蓝色渐变 */
	/*background-color: qlineargradient(spread:pad, x1:0.9801, y1:0, x2:0, y2:1, stop:0 rgba(245, 108, 108, 255), stop:1 rgba(253, 226, 226, 255));*/

}

QPushButton#btnAmplifier::hover{
	background-color:qlineargradient(spread:repeat, x1:1, y1:0, x2:0, y2:1, stop:0 rgba(72, 169, 170, 255), stop:1 rgba(62, 227, 159, 255));
}
QPushButton#btnOpenData::hover{
	background-color:qlineargradient(spread:repeat, x1:1, y1:0, x2:0, y2:1, stop:0 rgba(72, 169, 170, 255), stop:1 rgba(62, 227, 159, 255));
}
QPushButton#btnRecordData:hover{
	background-color:qlineargradient(spread:repeat, x1:1, y1:0, x2:0, y2:1, stop:0 rgba(72, 169, 170, 255), stop:1 rgba(62, 227, 159, 255));
}



/******************************************************************************************************************/


QGroupBox[GroupBox = "zml"]{
	font: 20px "Arial";
	border: 1px solid rgba(167, 167, 167);  
    	border-radius: 5px;  
	border-image: url(:/icon/image/bg8.png)
}
QGroupBox[GroupBox = "zml"]::title{
	color:#65bdd6;
	left:40px;
	subcontrol-origin: margin;
}




/******************************************************************************************************************/


QProgressBar#DesignProgressBar{
	border-radius:10px;
	background-color: rgb(255, 255, 255);
	text-align: center;
	height:20px;
	font: 18px "Arial"; 
}
/*进度块样式*/
QProgressBar#DesignProgressBar::chunk{
	border-radius:10px;
	background-color: rgba(2, 205, 156);
	/*margin: 10px;*//*控制进度块高*/	
}
QProgressBar#DesignProgressBar:hover{

}
