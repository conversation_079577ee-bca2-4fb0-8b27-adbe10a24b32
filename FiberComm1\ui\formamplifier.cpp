#include "formamplifier.h"
#include "ui_formamplifier.h"
#include "netconfigsettings.h"
#include "global.h"

#include <QTimer>
#include <QBitArray>
#include <QDebug>

using namespace Common;

FormAmplifier::FormAmplifier(const QString& name, API_Amplifier* api_amplifier, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::FormAmplifier), m_name(name), m_amplifier(api_amplifier)
{
    ui->setupUi(this);

    init();
}

FormAmplifier::~FormAmplifier()
{
    delete ui;
}

void FormAmplifier::init()
{
    ui->channel_id_A001->addItem("低噪声通道", 0);
    ui->channel_id_A001->addItem("高功率通道", 1);

    ui->switch_status_A001->addItem("关", 0);
    ui->switch_status_A001->addItem("开", 1);

    ui->channel_id_A002->addItem("低噪声通道", 0);
    ui->channel_id_A002->addItem("高功率通道", 1);

    ui->work_mode_A002->addItem("ACC模式", 0);
    ui->work_mode_A002->addItem("APC模式", 1);

    ui->pump_id_A003->addItem("泵浦1", 0);
    ui->pump_id_A003->addItem("泵浦2", 1);

    ui->channel_id_A004->addItem("低噪声通道", 0);
    ui->channel_id_A004->addItem("高功率通道", 1);

    ui->online_status_A007->addItem("出厂状态", 0xAA);
    ui->online_status_A007->addItem("保存设置", 0x55);

    QTimer* timer = new QTimer(this);
    timer->setInterval(1000);

    QList<QLabel*> labelList = findChildren<QLabel*>();

    foreach(QLabel* label, labelList)
    {
        if(!label->styleSheet().isEmpty())
        {
            label->setFixedSize(Common::FIXED_WIDTH, Common::FIXED_HEIGHT);
        }
    }

    connect(timer, &QTimer::timeout, this, [=](){

        if(m_amplifier->isLinked()){

            QueryUnitResponse query_status;
            m_amplifier->getQueryStatus(query_status);

            ui->module_temp->setText(QString::number(query_status.module_temp * 0.1, 'f', 1));

            ui->module_input_vol->setText(QString::number(query_status.module_input_vol));

            QByteArray byte_module_status;
            byte_module_status.append(query_status.module_status);
            QBitArray bit_module_status = QBitArray::fromBits(byte_module_status, 8);

            if(bit_module_status.at(7))
            {
                ui->noise_mode->setText("APC模式");
            }
            else
            {
                ui->noise_mode->setText("ACC模式");
            }

            if(bit_module_status.at(6))
            {
                ui->power_mode->setText("APC模式");
            }
            else
            {
                ui->power_mode->setText("ACC模式");
            }

            if(bit_module_status.at(5))
            {
                ui->noise_switch->setText("开");
            }
            else
            {
                ui->noise_switch->setText("关");
            }

            if(bit_module_status.at(4))
            {
                ui->power_switch->setText("开");
            }
            else
            {
                ui->power_switch->setText("关");
            }

            ui->noise_input_power->setText(QString::number(query_status.low_noise_input_freq * 0.01, 'f', 2));
            ui->power_input_power->setText(QString::number(query_status.high_power_input_freq * 0.01, 'f', 2));
            ui->noise_output_power->setText(QString::number(query_status.low_noise_output_freq * 0.01, 'f', 2));
            ui->power_output_power->setText(QString::number(query_status.high_power_output_freq * 0.01, 'f', 2));
            ui->current_1->setText(QString::number(query_status.pump_current_1));
            ui->temp_1->setText(QString::number(query_status.pump_temp_1 * 0.1, 'f', 1));
            ui->backlight_current_1->setText(QString::number(query_status.pump_bak_current_1 * 0.001, 'f', 3));
            ui->tec_current_1->setText(QString::number(query_status.pump_tec_current_1 * 0.1, 'f', 1));
            ui->current_2->setText(QString::number(query_status.pump_current_2));
            ui->temp_2->setText(QString::number(query_status.pump_temp_2 * 0.1, 'f', 1));
            ui->backlight_current_2->setText(QString::number(query_status.pump_bak_current_2 * 0.001, 'f', 3));
            ui->tec_current_2->setText(QString::number(query_status.pump_tec_current_2 * 0.1, 'f', 1));

            QByteArray byte_module_warn;
            byte_module_warn.append(query_status.module_warn);
            QBitArray bit_module_warn = QBitArray::fromBits(byte_module_warn, 8);

            if(bit_module_warn.at(7))
            {
                setLightColor(ui->module_warn_high_temp, YELLOW);
            }
            else
            {
                setLightColor(ui->module_warn_high_temp, GRAY);
            }

            if(bit_module_warn.at(6))
            {
                setLightColor(ui->module_warn_low_temp, YELLOW);
            }
            else
            {
                setLightColor(ui->module_warn_low_temp, GRAY);
            }

            if(bit_module_warn.at(5))
            {
                setLightColor(ui->module_warn_input_power, YELLOW);
            }
            else
            {
                setLightColor(ui->module_warn_input_power, GRAY);
            }

            if(bit_module_warn.at(4))
            {
                setLightColor(ui->module_warn_output_power, YELLOW);
            }
            else
            {
                setLightColor(ui->module_warn_output_power, GRAY);
            }

            QByteArray byte_power_warn;
            byte_power_warn.append(query_status.power_warn);
            QBitArray bit_power_warn = QBitArray::fromBits(byte_power_warn, 8);

            if(bit_power_warn.at(7))
            {
                setLightColor(ui->noise_warn_input_high_power, YELLOW);
            }
            else
            {
                setLightColor(ui->noise_warn_input_high_power, GRAY);
            }

            if(bit_power_warn.at(6))
            {
                setLightColor(ui->noise_warn_input_low_power, YELLOW);
            }
            else
            {
                setLightColor(ui->noise_warn_input_low_power, GRAY);
            }

            if(bit_power_warn.at(5))
            {
                setLightColor(ui->power_warn_input_high_power, YELLOW);
            }
            else
            {
                setLightColor(ui->power_warn_input_high_power, GRAY);
            }

            if(bit_power_warn.at(4))
            {
                setLightColor(ui->power_warn_input_low_power, YELLOW);
            }
            else
            {
                setLightColor(ui->power_warn_input_low_power, GRAY);
            }

            if(bit_power_warn.at(3))
            {
                setLightColor(ui->noise_warn_output_high_power, YELLOW);
            }
            else
            {
                setLightColor(ui->noise_warn_output_high_power, GRAY);
            }

            if(bit_power_warn.at(2))
            {
                setLightColor(ui->noise_warn_output_low_power, YELLOW);
            }
            else
            {
                setLightColor(ui->noise_warn_output_low_power, GRAY);
            }

            if(bit_power_warn.at(1))
            {
                setLightColor(ui->power_warn_output_high_power, YELLOW);
            }
            else
            {
                setLightColor(ui->power_warn_output_high_power, GRAY);
            }

            if(bit_power_warn.at(0))
            {
                setLightColor(ui->power_warn_output_low_power, YELLOW);
            }
            else
            {
                setLightColor(ui->power_warn_output_low_power, GRAY);
            }

            QByteArray byte_pump_warn_1;
            byte_pump_warn_1.append(query_status.pump_warn_1);
            QBitArray bit_pump_warn_1 = QBitArray::fromBits(byte_pump_warn_1, 8);

            if(bit_pump_warn_1.at(7))
            {
                setLightColor(ui->warn_current_high_1, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_current_high_1, GRAY);
            }

            if(bit_pump_warn_1.at(6))
            {
                setLightColor(ui->warn_current_low_1, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_current_low_1, GRAY);
            }

            if(bit_pump_warn_1.at(5))
            {
                setLightColor(ui->warn_temp_high_1, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_temp_high_1, GRAY);
            }

            if(bit_pump_warn_1.at(4))
            {
                setLightColor(ui->warn_temp_low_1, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_temp_low_1, GRAY);
            }

            if(bit_pump_warn_1.at(3))
            {
                setLightColor(ui->warn_backlight_high_1, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_backlight_high_1, GRAY);
            }

            if(bit_pump_warn_1.at(2))
            {
                setLightColor(ui->warn_backlight_low_1, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_backlight_low_1, GRAY);
            }

            if(bit_pump_warn_1.at(1))
            {
                setLightColor(ui->warn_tec_current_high_1, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_tec_current_high_1, GRAY);
            }

            if(bit_pump_warn_1.at(0))
            {
                setLightColor(ui->warn_tec_current_low_1, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_tec_current_low_1, GRAY);
            }

            QByteArray byte_pump_warn_2;
            byte_pump_warn_2.append(query_status.pump_warn_2);
            QBitArray bit_pump_warn_2 = QBitArray::fromBits(byte_pump_warn_2, 8);

            if(bit_pump_warn_2.at(7))
            {
                setLightColor(ui->warn_current_high_2, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_current_high_2, GRAY);
            }

            if(bit_pump_warn_2.at(6))
            {
                setLightColor(ui->warn_current_low_2, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_current_low_2, GRAY);
            }

            if(bit_pump_warn_2.at(5))
            {
                setLightColor(ui->warn_temp_high_2, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_temp_high_2, GRAY);
            }

            if(bit_pump_warn_2.at(4))
            {
                setLightColor(ui->warn_temp_low_2, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_temp_low_2, GRAY);
            }

            if(bit_pump_warn_2.at(3))
            {
                setLightColor(ui->warn_backlight_high_2, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_backlight_high_2, GRAY);
            }

            if(bit_pump_warn_2.at(2))
            {
                setLightColor(ui->warn_backlight_low_2, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_backlight_low_2, GRAY);
            }

            if(bit_pump_warn_2.at(1))
            {
                setLightColor(ui->warn_tec_current_high_2, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_tec_current_high_2, GRAY);
            }

            if(bit_pump_warn_2.at(0))
            {
                setLightColor(ui->warn_tec_current_low_2, YELLOW);
            }
            else
            {
                setLightColor(ui->warn_tec_current_low_2, GRAY);
            }

            GetOnlineStatusResponse online_status;
            m_amplifier->getOnlineStatus(online_status);

            switch(online_status.online_status) {
            case 0xAA:
                ui->online_status->setText("出厂状态");
                break;
            case 0x55:
                ui->online_status->setText("保存设置");
                break;
            default:
                ui->online_status->setText("未知定义");
                break;
            }
        }
        else {
            ui->module_temp->setText(OFFLINE_VALUE);
            ui->module_input_vol->setText(OFFLINE_VALUE);
            ui->noise_mode->setText(OFFLINE_VALUE);
            ui->power_mode->setText(OFFLINE_VALUE);
            ui->noise_switch->setText(OFFLINE_VALUE);
            ui->power_switch->setText(OFFLINE_VALUE);
            ui->noise_input_power->setText(OFFLINE_VALUE);
            ui->power_input_power->setText(OFFLINE_VALUE);
            ui->noise_output_power->setText(OFFLINE_VALUE);
            ui->power_output_power->setText(OFFLINE_VALUE);
            ui->current_1->setText(OFFLINE_VALUE);
            ui->temp_1->setText(OFFLINE_VALUE);
            ui->backlight_current_1->setText(OFFLINE_VALUE);
            ui->tec_current_1->setText(OFFLINE_VALUE);
            ui->current_2->setText(OFFLINE_VALUE);
            ui->temp_2->setText(OFFLINE_VALUE);
            ui->backlight_current_2->setText(OFFLINE_VALUE);
            ui->tec_current_2->setText(OFFLINE_VALUE);
            setLightColor(ui->module_warn_high_temp, GRAY);
            setLightColor(ui->module_warn_low_temp, GRAY);
            setLightColor(ui->module_warn_input_power, GRAY);
            setLightColor(ui->module_warn_output_power, GRAY);
            setLightColor(ui->noise_warn_input_high_power, GRAY);
            setLightColor(ui->noise_warn_input_low_power, GRAY);
            setLightColor(ui->power_warn_input_high_power, GRAY);
            setLightColor(ui->power_warn_input_low_power, GRAY);
            setLightColor(ui->noise_warn_output_high_power, GRAY);
            setLightColor(ui->noise_warn_output_low_power, GRAY);
            setLightColor(ui->power_warn_output_high_power, GRAY);
            setLightColor(ui->power_warn_output_low_power, GRAY);
            setLightColor(ui->warn_current_high_1, GRAY);
            setLightColor(ui->warn_current_low_1, GRAY);
            setLightColor(ui->warn_temp_high_1, GRAY);
            setLightColor(ui->warn_temp_low_1, GRAY);
            setLightColor(ui->warn_backlight_high_1, GRAY);
            setLightColor(ui->warn_backlight_low_1, GRAY);
            setLightColor(ui->warn_tec_current_high_1, GRAY);
            setLightColor(ui->warn_tec_current_low_1, GRAY);
            setLightColor(ui->warn_current_high_2, GRAY);
            setLightColor(ui->warn_current_low_2, GRAY);
            setLightColor(ui->warn_temp_high_2, GRAY);
            setLightColor(ui->warn_temp_low_2, GRAY);
            setLightColor(ui->warn_backlight_high_2, GRAY);
            setLightColor(ui->warn_backlight_low_2, GRAY);
            setLightColor(ui->warn_tec_current_high_2, GRAY);
            setLightColor(ui->warn_tec_current_low_2, GRAY);
            ui->online_status->setText(OFFLINE_VALUE);
        }

    });

    timer->start();
}

void FormAmplifier::setLightColor(QLabel *label, const FormAmplifier::LightColor color)
{
    switch (color) {
    case GRAY:
        label->setStyleSheet("background-image: url(:/icon/image/Gray_status.png);");
        break;
    case GREEN:
        label->setStyleSheet("background-image: url(:/icon/image/Green_status.png);");
        break;
    case RED:
        label->setStyleSheet("background-image: url(:/icon/image/Red_status.png);");
        break;
    case YELLOW:
        label->setStyleSheet("background-image: url(:/icon/image/Yellow_status.png);");
        break;
    }
}

void FormAmplifier::on_btnA001_clicked()
{
    quint8 channel_id = ui->channel_id_A001->itemData(ui->channel_id_A001->currentIndex(), Qt::UserRole).toUInt();

    quint8 switch_status =  ui->switch_status_A001->itemData(ui->switch_status_A001->currentIndex(), Qt::UserRole).toUInt();

    m_amplifier->controlSetSwitch(channel_id, switch_status);
}

void FormAmplifier::on_btnA002_clicked()
{
    quint8 channel_id = ui->channel_id_A002->itemData(ui->channel_id_A002->currentIndex(), Qt::UserRole).toUInt();

    quint8 work_mode =  ui->work_mode_A002->itemData(ui->work_mode_A002->currentIndex(), Qt::UserRole).toUInt();

    m_amplifier->controlSetWorkMode(channel_id, work_mode);
}

void FormAmplifier::on_btnA003_clicked()
{
    quint8 pump_id = ui->pump_id_A003->itemData(ui->pump_id_A003->currentIndex(), Qt::UserRole).toUInt();

    quint16 current = ui->current_A003->text().toUInt();

    m_amplifier->controlSetCurrent(pump_id, current);
}

void FormAmplifier::on_btnA004_clicked()
{
    quint8 channel_id = ui->channel_id_A004->itemData(ui->channel_id_A004->currentIndex(), Qt::UserRole).toUInt();

    double power = ui->output_power_A004->text().toDouble();

    m_amplifier->controlSetOutputPower(channel_id, power);
}

void FormAmplifier::on_btnA007_clicked()
{
    quint8 online_status = ui->online_status_A007->itemData(ui->online_status_A007->currentIndex(), Qt::UserRole).toUInt();

    m_amplifier->controlSetOnlineStatus(online_status);
}
