#ifndef FORMAMPLIFIER_H
#define FORMAMPLIFIER_H

#include <QWidget>
#include <QLabel>

#include "api_amplifier.h"

namespace Ui {
class FormAmplifier;
}

class FormAmplifier : public QWidget
{
    Q_OBJECT

public:
    typedef enum LIGHT_COLOR
    {
        GRAY, GREEN, RED, Y<PERSON>LOW
    }LightColor;

    explicit FormAmplifier(const QString& name, API_Amplifier* api_amplifier = nullptr, QWidget *parent = nullptr);
    ~FormAmplifier();

private slots:
    void on_btnA001_clicked();

    void on_btnA002_clicked();

    void on_btnA003_clicked();

    void on_btnA004_clicked();

    void on_btnA007_clicked();

private:
    void init();

    void setLightColor(QLabel* label, const LightColor color);

private:
    Ui::FormAmplifier *ui;

    API_Amplifier* m_amplifier = nullptr;

    QString m_name = "";
};

#endif // FORMAMPLIFIER_H
