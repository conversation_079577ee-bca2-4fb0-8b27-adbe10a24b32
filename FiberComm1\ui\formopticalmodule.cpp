#include "formopticalmodule.h"
#include "ui_formopticalmodule.h"
#include "dev_opticalmodule.h"
#include "logs.h"
#include "netconfigsettings.h"

#include <QTimer>
#include <QByteArray>
#include <QDateTime>
#include <QDesktopServices>
#include <QUrl>
#include <QDir>
#include <QtMath>

static quint64 s_total_bit_count = 0;
static quint64 s_error_bit_count = 0;

FormOpticalModule::FormOpticalModule(const QString name, API_OpticalModule* api_optical_module, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::FormOpticalModule),
    m_api_optical_module(api_optical_module), m_name(name)
{
    ui->setupUi(this);

    init();
}

FormOpticalModule::~FormOpticalModule()
{
    delete ui;
}

void FormOpticalModule::init()
{
    connect(m_api_optical_module, &API_OpticalModule::signalControlResult, this, [=](const QString& control_result){
        ui->control_result->setText(control_result);
    });

    ui->ctrl_module_status->addItem("复位", "0x8000");
    ui->ctrl_module_status->addItem("低功耗状态", "0x4000");
    ui->ctrl_module_status->addItem("通道关闭", "0x2000");
    ui->ctrl_module_status->addItem("通道打开", "0x0000");
    ui->ctrl_module_status->setCurrentIndex(1);

    ui->ctrl_freq_interval->addItem("100GHz", 0x0);
    ui->ctrl_freq_interval->addItem("50GHz", 0x2);
    ui->ctrl_freq_interval->addItem("33GHz", 0x4);
    ui->ctrl_freq_interval->addItem("25GHz", 0x6);
    ui->ctrl_freq_interval->addItem("12.5GHz", 0x8);
    ui->ctrl_freq_interval->addItem("6.25GHz", 0x10);
    ui->ctrl_freq_interval->setCurrentIndex(1);

    QTimer* timer = new QTimer(this);
    timer->setInterval(1000);
    connect(timer, &QTimer::timeout, this, [=](){

        displayModuleStatus();

        displayOutputPower();

        displayInputPower();

        displaySNR();

        displayCarrierFreq();

        displayCarrierFreqOffset();

        displayPRBSBitCount();

        recordStatus();

    });
    timer->start();
}

void FormOpticalModule::displayModuleStatus()
{
    CacheValue queryStatus;

    m_api_optical_module->getQueryStatus(queryStatus);

    QString result = queryStatus.REG_STATUS;

    quint16 sta_module_status = result.toUInt(nullptr, 16);

    switch (sta_module_status) {
    case 0xffff:
        setLightColor(ui->sta_config_status, RED);      //复位
        setLightColor(ui->sta_switch_status, GRAY);     //传输通道关闭
        setLightColor(ui->sta_power_status, GRAY);      //低功耗
        break;
    case 0x0080:
        setLightColor(ui->sta_config_status, GREEN);    //就绪
        setLightColor(ui->sta_switch_status, GRAY);     //传输通道关闭
        setLightColor(ui->sta_power_status, GREEN);     //高功耗
        break;
    case 0x0040:    //立即转换为复位状态
        break;
    case 0x0020:
        setLightColor(ui->sta_config_status, GREEN);    //就绪
        setLightColor(ui->sta_switch_status, GREEN);    //传输通道打开
        setLightColor(ui->sta_power_status, GREEN);     //高功耗
        break;
    case 0x0002:
        setLightColor(ui->sta_config_status, GREEN);    //就绪
        setLightColor(ui->sta_switch_status, GRAY);     //传输通道关闭
        setLightColor(ui->sta_power_status, GRAY);      //低功耗
        break;
    default:
        break;
    }
}

void FormOpticalModule::displayOutputPower()
{
    CacheValue queryStatus;

    m_api_optical_module->getQueryStatus(queryStatus);

    qint16 sta_output_power = queryStatus.REG_OUTPUT_POWER.toUShort(nullptr, 16);

    ui->output_power->setText(QString::number(sta_output_power * 0.01, 'f', 2));
}

void FormOpticalModule::displayInputPower()
{
    CacheValue queryStatus;

    m_api_optical_module->getQueryStatus(queryStatus);

    qint16 sta_input_power = queryStatus.REG_INPUT_POWER.toUShort(nullptr, 16);

    ui->input_power->setText(QString::number(sta_input_power * 0.01, 'f', 2));
}

void FormOpticalModule::displaySNR()
{
    CacheValue queryStatus;

    m_api_optical_module->getQueryStatus(queryStatus);

    quint16 sta_snr = queryStatus.REG_SNR.toUShort(nullptr, 16);

    ui->snr->setText(QString::number(sta_snr * 0.1, 'f', 1));
}

void FormOpticalModule::displayCarrierFreq()
{
    CacheValue queryStatus;

    m_api_optical_module->getQueryStatus(queryStatus);

    quint16 carrier_freq_1 = queryStatus.REG_SENT_CARRIER_FREQ_1.toUShort(nullptr, 16);

    quint16 carrier_freq_2 = queryStatus.REG_SENT_CARRIER_FREQ_2.toUShort(nullptr, 16);

    ui->sent_power->setText(QString::number(carrier_freq_2 * 0.05 + carrier_freq_1 * 1000, 'f', 2));
}

void FormOpticalModule::displayCarrierFreqOffset()
{
    CacheValue queryStatus;

    m_api_optical_module->getQueryStatus(queryStatus);

    qint16 sta_carrie_offset = queryStatus.REG_CARRIER_FREQ_OFFSET.toUShort(nullptr, 16);

    ui->freq_offset->setText(QString::number(sta_carrie_offset));
}

void FormOpticalModule::displayPRBSBitCount()
{
    CacheValue queryStatus;

    m_api_optical_module->getQueryStatus(queryStatus);

    quint16 sta_bit_count_1 = queryStatus.REG_BIT_COUNT_1.toUShort(nullptr, 16);
    quint16 sta_bit_count_2 = queryStatus.REG_BIT_COUNT_2.toUShort(nullptr, 16);
    quint16 sta_bit_count_3 = queryStatus.REG_BIT_COUNT_3.toUShort(nullptr, 16);
    quint16 sta_bit_count_4 = queryStatus.REG_BIT_COUNT_4.toUShort(nullptr, 16);


    quint64 sta_bit_count = ((sta_bit_count_1 + 1) * qPow(2, 48))
            + ((sta_bit_count_2 + 1) * qPow(2, 32))
            + ((sta_bit_count_3 + 1) * qPow(2, 16))
            + sta_bit_count_4 - 3;

    quint16 sta_error_bit_count_1 = queryStatus.REG_ERROR_BIT_COUNT_1.toUShort(nullptr, 16);
    quint16 sta_error_bit_count_2 = queryStatus.REG_ERROR_BIT_COUNT_2.toUShort(nullptr, 16);
    quint16 sta_error_bit_count_3 = queryStatus.REG_ERROR_BIT_COUNT_3.toUShort(nullptr, 16);
    quint16 sta_error_bit_count_4 = queryStatus.REG_ERROR_BIT_COUNT_4.toUShort(nullptr, 16);

    quint64 sta_error_bit_count = ((sta_error_bit_count_1 + 1) * qPow(2, 48))
            + ((sta_error_bit_count_2 + 1) * qPow(2, 32))
            + ((sta_error_bit_count_3 + 1) * qPow(2, 16))
            + sta_error_bit_count_4 - 3;

    if((sta_bit_count > s_total_bit_count) && (sta_error_bit_count > s_error_bit_count))
    {
        ui->bit_count->setText(QString::number(sta_error_bit_count / sta_bit_count, 'f', 3));
    }

    s_total_bit_count = sta_bit_count;
    s_error_bit_count = sta_error_bit_count;
}

void FormOpticalModule::recordStatus()
{
    if(m_record_status)
    {
        QString strBody = QTime::currentTime().toString("hh:mm:ss.zzz,");

        QString reg_addr = "";

        for(int i = 0; i < OpticalModule::QUERY_REG_ADDR_LIST.size(); i++)
        {
            reg_addr = "0x" + QString::number(OpticalModule::QUERY_REG_ADDR_LIST.at(i), 16);

//            strBody += QString("%1,").arg(m_api_optical_module->getStatus(reg_addr));
        }

        strBody.chop(1);

        strBody += "\n";

        m_record_file->write(strBody.toLatin1());
    }
}

void FormOpticalModule::setLightColor(QLabel *label, const FormOpticalModule::LightColor color)
{
    switch (color) {
    case GRAY:
        label->setStyleSheet("background-image: url(:/icon/image/Gray_status.png);");
        break;
    case GREEN:
        label->setStyleSheet("background-image: url(:/icon/image/Green_status.png);");
        break;
    case RED:
        label->setStyleSheet("background-image: url(:/icon/image/Red_status.png);");
        break;
    case YELLOW:
        label->setStyleSheet("background-image: url(:/icon/image/Yellow_status.png);");
        break;
    }
}

void FormOpticalModule::on_btnTestCommand_clicked()
{
//   m_api_optical_module->sendData(QString("%1\r\n").arg(ui->control_command->text()));
}

void FormOpticalModule::on_btnSet_b010_clicked()
{
    QString ctrl_value = ui->ctrl_module_status->itemData(ui->ctrl_module_status->currentIndex(), Qt::UserRole).toString();

    m_api_optical_module->controlModuleStatus(ctrl_value);
}

void FormOpticalModule::on_btnSet_b410_clicked()
{
    double output_power = ui->ctrl_output_power->text().toDouble();



    m_api_optical_module->controlOutputPower(output_power);
}

void FormOpticalModule::on_btnSet_b400_clicked()
{
    quint16 freq_interval = ui->ctrl_freq_interval->itemData(ui->ctrl_freq_interval->currentIndex(), Qt::UserRole).toUInt(nullptr);

    quint16 channel_num = ui->ctrl_channel_num->text().toUInt();

    m_api_optical_module->controlCarrierFreq(freq_interval, channel_num);
}

void FormOpticalModule::on_btnSet_b430_clicked()
{
    qint16 ctrl_value = (qint16)ui->ctrl_freq_compensate->text().toInt();

    m_api_optical_module->controlCarrierFreqOffset(ctrl_value);
}

void FormOpticalModule::on_btnRecord_clicked()
{
    if(!m_record_status){

        m_record_status = true;

        m_record_file = new QFile(QString("%1/OpticalModule_%2.csv")
                                  .arg(QCoreApplication::applicationDirPath())
                                  .arg(QDateTime::currentDateTime().toString("yyMMdd_hhmmss")));

        if(!m_record_file->open(QIODevice::WriteOnly)) return;

        QString strHead = "Time,";

        for(int i = 0; i < OpticalModule::QUERY_REG_ADDR_LIST.size(); i++)
        {
            strHead += QString("%1,").arg(QString::number(OpticalModule::QUERY_REG_ADDR_LIST.at(i), 16));
        }

        strHead.chop(1);

        strHead += "\n";

        m_record_file->write(strHead.toLatin1());

        m_record_file->flush();

        ui->btnRecord->setStyleSheet("background-color: rgb(0, 255, 0);");
    }
    else {

        m_record_status = false;

        m_record_file->flush();

        m_record_file->close();

        m_record_file = nullptr;

        ui->btnRecord->setStyleSheet("background-color: rgb(255, 255, 255);");
    }
}

void FormOpticalModule::on_btnRecord_2_clicked()
{
    if(m_record_file != nullptr)
    {
        m_record_file->flush();

        QString file_path = "file:///" + m_record_file->fileName();

        QDesktopServices::openUrl(QUrl(file_path));
    }
}
