QT       += core gui serialport network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17

INCLUDEPATH += $$PWD/api
INCLUDEPATH += $$PWD/ui
INCLUDEPATH += $$PWD/tool
LIBS += -lwsock32 -lws2_32

CONFIG(release, debug|release){
DESTDIR = $$PWD/bin
}
else: CONFIG(debug, debug|release)
{
DESTDIR = $$PWD/bin_d
LIBS += -L$$PWD/QtHiRedis_Lib_PATH/libs -lQtHiRedis
INCLUDEPATH += $$PWD/QtHiRedis_Lib_PATH/include
HEADERS += $$PWD/QtHiRedis_Lib_PATH/include/adapters/qt.h \
    api/api_redisclient.h
}

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    api/api_amplifier.cpp \
    api/api_opticalmodule.cpp \
    api/api_redisclient.cpp \
    api/qttelnet.cpp \
    main.cpp \
    mainwindow.cpp \
    tool/logs.cpp \
    tool/netconfigsettings.cpp \
    ui/formamplifier.cpp \
    ui/formcombineddev.cpp \
    ui/formopticalmodule.cpp

HEADERS += \
    api/api_amplifier.h \
    api/api_opticalmodule.h \
    api/dev_amplifier.h \
    api/dev_opticalmodule.h \
    api/qttelnet.h \
    mainwindow.h \
    tool/global.h \
    tool/logs.h \
    tool/netconfigsettings.h \
    ui/formamplifier.h \
    ui/formcombineddev.h \
    ui/formopticalmodule.h

FORMS += \
    mainwindow.ui \
    ui/formamplifier.ui \
    ui/formcombineddev.ui \
    ui/formopticalmodule.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

RESOURCES += \
    resource.qrc

RC_ICONS = monitor.ico
