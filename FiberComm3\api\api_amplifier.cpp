#include "api_amplifier.h"
#include "winsock2.h"
#include "global.h"

#include <QDebug>
#include <QTimer>

const quint32 QUERY_TIME_INTERVAL = 2000;

API_Amplifier::API_Amplifier(const QString& name, const QString& port_name, const qint32 baudRate, QObject *parent)
    : QObject(parent), m_port_name(port_name), m_baud_rate(baudRate), m_name(name), m_log(new Logs(m_name))
{
    init();
}

API_Amplifier::~API_Amplifier()
{
    m_serialPort->clear();

    m_serialPort->close();
}

void API_Amplifier::getQueryStatus(QueryUnitResponse &status)
{
    status = m_query_status;
}

void API_Amplifier::getOnlineStatus(GetOnlineStatusResponse &status)
{
    status = m_online_status;
}

bool API_Amplifier::isLinked()
{
    return !m_elapsed_timer.hasExpired(Common::QUERY_EXPIRED_TIME);
}

void API_Amplifier::configInfo()
{
    m_serialPort = new QSerialPort(this);
    m_serialPort->setPortName(m_port_name);
    m_serialPort->setBaudRate(m_baud_rate);
    m_serialPort->setStopBits(QSerialPort::OneStop);
    m_serialPort->setDataBits(QSerialPort::Data8);
    m_serialPort->setParity(QSerialPort::NoParity);

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开始配置串口信息.").arg(m_name), Logs::LEVEL_INFO);

    bool result = m_serialPort->open(QIODevice::ReadWrite);

    if(result){

        startQuery();

        m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:串口打开成功,开始接收串口数据...").arg(m_name), Logs::LEVEL_INFO);

        connect(m_serialPort, &QSerialPort::readyRead, this, [=](){

            QTimer::singleShot(50, this, [=](){

                QByteArray recv_buf = m_serialPort->readAll();


                if(recv_buf.size() < 8) return ;

                QByteArray byteCmdid = recv_buf.mid(4, 2);

                std::reverse(byteCmdid.begin(), byteCmdid.end());

                quint16 uCmdid = 0;

                ::memcpy(&uCmdid, byteCmdid.data(), sizeof(quint16));

                QByteArray data_field_buf = recv_buf.mid(8);

                m_elapsed_timer.restart();

                if(uCmdid == CMDID_QUERY_UNIT_RESPONSE)
                {
                    QueryUnitResponse query_status;

                    ::memcpy(&query_status, data_field_buf.data(), sizeof(query_status));

                    query_status.module_temp = reverse(query_status.module_temp);
                    query_status.module_input_vol = reverse(query_status.module_input_vol);
                    query_status.low_noise_input_freq = reverse(query_status.low_noise_input_freq);
                    query_status.high_power_input_freq = reverse(query_status.high_power_input_freq);
                    query_status.low_noise_output_freq = reverse(query_status.low_noise_output_freq);
                    query_status.high_power_output_freq = reverse(query_status.high_power_output_freq);
                    query_status.pump_current_1 = reverse(query_status.pump_current_1);
                    query_status.pump_temp_1 = reverse(query_status.pump_temp_1);
                    query_status.pump_bak_current_1 = reverse(query_status.pump_bak_current_1);
                    query_status.pump_tec_current_1 = reverse(query_status.pump_tec_current_1);
                    query_status.pump_current_2 = reverse(query_status.pump_current_2);
                    query_status.pump_temp_2 = reverse(query_status.pump_temp_2);
                    query_status.pump_bak_current_2 = reverse(query_status.pump_bak_current_2);
                    query_status.pump_tec_current_2 = reverse(query_status.pump_tec_current_2);

                    m_query_status = query_status;
                }
                else if(uCmdid == CMDID_SET_ONLINE_STATUS_REQUEST)
                {
                    GetOnlineStatusResponse online_status;

                    ::memcpy(&online_status, data_field_buf.data(), sizeof(online_status));

                    m_online_status = online_status;
                }

            });
        });
    }
    else {

        m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:串口打开失败.").arg(m_name), Logs::LEVEL_ERROR);
    }
}

void API_Amplifier::startQuery()
{
    QTimer* timer = new QTimer(this);

    timer->setInterval(QUERY_TIME_INTERVAL);

    connect(timer, &QTimer::timeout, this, [=](){

        queryStatus();

        queryOnlineStatus();
    });

    timer->start();

    m_elapsed_timer.start();
}

void API_Amplifier::queryStatus()
{
    QByteArray byteHead;

    getProtocol(byteHead, CMDID_QUERY_UNIT_RESPONSE, 0);

    QByteArray send_buf;

    send_buf.append(byteHead);

    send_buf.append(check_data_xor(byteHead));

    m_serialPort->write(send_buf);

//    qDebug() << send_buf.toHex();
}

void API_Amplifier::queryOnlineStatus()
{
    QByteArray byteHead;

    getProtocol(byteHead, CMDID_SET_ONLINE_STATUS_REQUEST, 0);

    QByteArray send_buf;

    send_buf.append(byteHead);

    send_buf.append(check_data_xor(byteHead));

    m_serialPort->write(send_buf);

//    qDebug() << send_buf.toHex();
}

void API_Amplifier::controlSetSwitch(quint8 channel_id, quint8 switch_status)
{
    QByteArray byteProtocol;
    getProtocol(byteProtocol, CMDID_SET_SWITCH_REQUEST, sizeof(SetSwitchRequest));

    SetSwitchRequest request;
    request.channel_id = channel_id;
    request.switch_status = switch_status;

    QByteArray byteDataField;
    byteDataField.resize(sizeof(SetSwitchRequest));
    ::memcpy(byteDataField.data(), &request, sizeof(SetSwitchRequest));

    QByteArray send_buf;
    send_buf.append(byteProtocol);
    send_buf.append(byteDataField);
    send_buf.append(check_data_xor(send_buf));

    m_serialPort->write(send_buf);

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:设置通道开关状态,{通道号:%2,设置开关状态:%3}.")
                                   .arg(m_name).arg(channel_id).arg(switch_status), Logs::LEVEL_INFO);
}

void API_Amplifier::controlSetWorkMode(quint8 channel_id, quint8 work_mode)
{
    QByteArray byteProtocol;
    getProtocol(byteProtocol, CMDID_SET_WORK_MODE_REQUEST, sizeof(SetWorkModeRequest));

    SetWorkModeRequest request;
    request.channel_id = channel_id;
    request.work_mode = work_mode;

    QByteArray byteDataField;
    byteDataField.resize(sizeof(SetWorkModeRequest));
    ::memcpy(byteDataField.data(), &request, sizeof(SetWorkModeRequest));

    QByteArray send_buf;
    send_buf.append(byteProtocol);
    send_buf.append(byteDataField);
    send_buf.append(check_data_xor(send_buf));

    m_serialPort->write(send_buf);

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:设置通道工作模式,{通道号:%2,设置工作模式:%3}.")
                                   .arg(m_name).arg(channel_id).arg(work_mode), Logs::LEVEL_INFO);
}

void API_Amplifier::controlSetCurrent(quint8 pump_id, quint16 current)
{
    QByteArray byteProtocol;
    getProtocol(byteProtocol, CMDID_SET_CURRENT_REQUEST, sizeof(SetCurrentRequest));

    SetCurrentRequest request;
    request.pump_id = pump_id;
    request.current = reverse(current);

    QByteArray byteDataField;
    byteDataField.resize(sizeof(SetCurrentRequest));
    ::memcpy(byteDataField.data(), &request, sizeof(SetCurrentRequest));

    QByteArray send_buf;
    send_buf.append(byteProtocol);
    send_buf.append(byteDataField);
    send_buf.append(check_data_xor(send_buf));

    m_serialPort->write(send_buf);

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:设置泵浦电流值,{泵浦编号:%2,设置电流值:%3}.")
                                   .arg(m_name).arg(pump_id).arg(current), Logs::LEVEL_INFO);
}

void API_Amplifier::controlSetOutputPower(quint8 channel_id, double output_power)
{
    QByteArray byteProtocol;
    getProtocol(byteProtocol, CMDID_SET_OUTPUT_POWER_REQUEST, sizeof(SetOutputPowerRequest));

    SetOutputPowerRequest request;
    request.channel_id = channel_id;
    request.output_power = reverse(static_cast<qint16>(output_power * 100));

    QByteArray byteDataField;
    byteDataField.resize(sizeof(SetOutputPowerRequest));
    ::memcpy(byteDataField.data(), &request, sizeof(SetOutputPowerRequest));

    QByteArray send_buf;
    send_buf.append(byteProtocol);
    send_buf.append(byteDataField);
    send_buf.append(check_data_xor(send_buf));

    m_serialPort->write(send_buf);

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:设置通道输出功率值,{通道号:%2,功率设置值:%3}.")
                                   .arg(m_name).arg(channel_id).arg(QString::number(output_power, 'f', 2)), Logs::LEVEL_INFO);

    qDebug() << send_buf.toHex();
}

void API_Amplifier::controlSetOnlineStatus(quint8 online_status)
{
    QByteArray byteProtocol;
    getProtocol(byteProtocol, CMDID_SET_ONLINE_STATUS_REQUEST, sizeof(SetOnlineStatusRequest));

    SetOnlineStatusRequest request;
    request.online_status = online_status;

    QByteArray byteDataField;
    byteDataField.resize(sizeof(SetOnlineStatusRequest));
    ::memcpy(byteDataField.data(), &request, sizeof(SetOnlineStatusRequest));

    QByteArray send_buf;
    send_buf.append(byteProtocol);
    send_buf.append(byteDataField);
    send_buf.append(check_data_xor(send_buf));

    m_serialPort->write(send_buf);

//    qDebug() << send_buf.toHex();

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:设置上电状态,{上电状态:%2}.")
                                   .arg(m_name).arg(online_status), Logs::LEVEL_INFO);
}

void API_Amplifier::getProtocol(QByteArray &frame_head, const quint16 cmdid, const quint16 len)
{
    QByteArray byteProtocol;
    byteProtocol.resize(4);
    int i = 0;
    byteProtocol[i++] = 0x55;
    byteProtocol[i++] = 0xaa;
    byteProtocol[i++] = 0xff;
    byteProtocol[i++] = 0xff;

    QByteArray byteCmdid;
    byteCmdid.resize(2);
    ::memcpy(byteCmdid.data(), &cmdid, sizeof(quint16));
    std::reverse(byteCmdid.begin(), byteCmdid.end());

    QByteArray byteLen;
    byteLen.resize(2);
    ::memcpy(byteLen.data(), &len, sizeof(quint16));
    std::reverse(byteLen.begin(), byteLen.end());

    byteProtocol.append(byteCmdid);
    byteProtocol.append(byteLen);

    frame_head = byteProtocol;
}

void API_Amplifier::init()
{
    configInfo();
}
