#include "api_opticalmodule.h"
#include "global.h"

#include <QTimer>
#include <QTime>
#include <QtMath>
#include <thread>
#include <QCoreApplication>
#include <QEventLoop>

const qint32 QUERY_TIME_INTERVAL_MS = 500;
const qint32 FIRST_QUERY_TIME_INTERVAL_MS = 60;

static quint32 query_count = 0;
static double scan_cnt = 0;
static qint16 scan_freq_tmp = 0;
using namespace OpticalModule;
using namespace std;

API_OpticalModule::API_OpticalModule(const QString name, QObject *parent)
    : QObject (parent), m_name(name), m_log(new Logs(m_name)), m_timer(new QTimer(this))
{
    init();
}

API_OpticalModule::~API_OpticalModule()
{
    if(m_log != nullptr)
    {
        delete m_log;
        m_log = nullptr;
    }
}

void API_OpticalModule::getQueryStatus(CacheValue &value)
{
    value = m_cache_values;
}

void API_OpticalModule::queryData()
{
    //tsh
    //    return;

    sendData(GET_STATUS);

    QTimer::singleShot(1 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_OUTPUT_POWER);
    });

    QTimer::singleShot(2 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_INPUT_POWER);
    });

    QTimer::singleShot(3 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_SNR);
    });

    QTimer::singleShot(4 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_SENT_CARRIER_FREQ_1);
    });

    QTimer::singleShot(5 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_SENT_CARRIER_FREQ_2);
    });

    QTimer::singleShot(6 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_CARRIER_FREQ_OFFSET);
    });

    QTimer::singleShot(7 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_BIT_COUNT_1);
    });

    QTimer::singleShot(8 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_ERROR_BIT_COUNT_1);
    });

    QTimer::singleShot(9 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_CONTROL_CARRIER_FREQ);
    });

    QTimer::singleShot(10 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_CONTROL_DOPPLER_VALUE);
    });

    QTimer::singleShot(11 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_CONTROL_OUTPUT_POWER);
    });

    QTimer::singleShot(12 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_TEMP);
    });

    QTimer::singleShot(13 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_EVM);
    });


    QTimer::singleShot(14 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_ERROR_CODE_RATE);
    });

    QTimer::singleShot(15 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_PRBS_STATUS);
    });

    QTimer::singleShot(16 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_MOD_STATUS);
    });

    QTimer::singleShot(17 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_BC04);
    });

    QTimer::singleShot(18 * FIRST_QUERY_TIME_INTERVAL_MS, this, [=](){
        sendData(GET_B050);
    });

    query_count ++;

    m_elapsed_timer.start();


    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开启寄存器定时查询.").arg(m_name), Logs::LEVEL_INFO);
}

bool API_OpticalModule::isLinked()
{
    return !m_elapsed_timer.hasExpired(Common::QUERY_EXPIRED_TIME);
}

void API_OpticalModule::controlModuleStatus(const QString &moduleStatus)
{
    sendData(OpticalModule::SET_STATUS + moduleStatus + OpticalModule::TAIL);


    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:发送控制光模块状态指令,{模块状态:%2}.").arg(m_name).arg(moduleStatus), Logs::LEVEL_INFO);
}

void API_OpticalModule::controlOutputPower(const double output_power)
{
    m_set_output_power = true;

    m_value_output_power = output_power;

    qint16 ctrl_value = (qint16)output_power * 100;

    QByteArray byte_value;

    byte_value.resize(2);

    ::memcpy(byte_value.data(), &ctrl_value, sizeof (qint16));

    std::reverse(byte_value.begin(),byte_value.end());

    sendData(OpticalModule::SET_OUTPUT_POWER + QString("0x%1\r\n").arg(QString(byte_value.toHex())));

    QTimer::singleShot(Common::CONTROL_EXPIRED_TIME, this, [=](){
        if(m_set_output_power)
        {
            m_set_output_power = false;

            m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:控制光模块的理想输出功率指令操作超时.").arg(m_name), Logs::LEVEL_WARN);
        }
    });

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:发送控制光模块的理想输出功率指令,{输出功率:%2}.").arg(m_name)
                     .arg(QString::number(output_power, 'f', 2)), Logs::LEVEL_INFO);
}

void API_OpticalModule::controlCarrierFreq(const quint16 freq_interval, const quint16 channel_num)
{

    m_set_carrier_freq = false;

    m_value_carrier_freq = channel_num;

    quint16 ctrl_value = channel_num + 8192;

    sendData(OpticalModule::SET_CARRIER_FREQ + QString("0x%1\r\n").arg(QString::number(ctrl_value, 16)));


    QTimer::singleShot(Common::CONTROL_EXPIRED_TIME, this, [=](){
        if(m_set_carrier_freq)
        {
            m_set_carrier_freq = false;

            m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:控制光模块的载波频率值指令操作超时.").arg(m_name), Logs::LEVEL_WARN);
        }
    });

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:发送控制光模块的载波频率值指令,{频率间隔:%2,:通道个数:%3}.").arg(m_name)
                     .arg(freq_interval)
                     .arg(channel_num), Logs::LEVEL_INFO);
}

void API_OpticalModule::controlCarrierFreqOffset(const qint16 freq_compensate)
{

    QByteArray byte_value;

    byte_value.resize(2);

    ::memcpy(byte_value.data(), &freq_compensate, sizeof (qint16));

    std::reverse(byte_value.begin(),byte_value.end());

    sendData(OpticalModule::SET_CARRIER_FREQ_PRESET + QString("0x%1\r\n").arg(QString(byte_value.toHex())));


    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:发送控制光模块CFO频率预补偿指令,{频率补偿值:%2}.").arg(m_name)
                     .arg(freq_compensate), Logs::LEVEL_INFO);
}

void API_OpticalModule::controlSentPRBSOpen()
{

    controlPRBSClose();

    QTimer::singleShot(2000, this, [=](){

        m_status_sent_open_prbs = true;

        sendData("cfp2 read b011\r\n");

        QTimer::singleShot(Common::CONTROL_EXPIRED_TIME, this, [=](){
            if(m_status_sent_open_prbs)
            {
                m_status_sent_open_prbs = false;

                m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:打开发端prbs操作超时.").arg(m_name), Logs::LEVEL_WARN);
            }
        });

        m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开始打开发端prbs.").arg(m_name), Logs::LEVEL_INFO);
    });
}

void API_OpticalModule::controlRecvPRBSOpen()
{
    controlPRBSClose();

    QTimer::singleShot(2000, this, [=](){
        m_status_recv_open_prbs = true;

        sendData("cfp2 read b012\r\n");

        QTimer::singleShot(Common::CONTROL_EXPIRED_TIME, this, [=](){
            if(m_status_recv_open_prbs)
            {
                m_status_recv_open_prbs = false;

                m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:打开收端prbs操作超时.").arg(m_name), Logs::LEVEL_WARN);
            }
        });

        m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开始打开收端prbs.").arg(m_name), Logs::LEVEL_INFO);
    });
}

void API_OpticalModule::controlPRBSClose()
{
    m_status_close_all_prbs = true;

    sendData("cfp2 read b011\r\n");

    QTimer::singleShot(Common::CONTROL_EXPIRED_TIME, this, [=](){
        if(m_status_close_all_prbs)
        {
            m_status_close_all_prbs = false;

            m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:关闭prbs收/发计数操作超时.").arg(m_name), Logs::LEVEL_WARN);
        }
    });

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开始关闭prbs收/发计数.").arg(m_name), Logs::LEVEL_INFO);
}

void API_OpticalModule::controlModOpen()
{
    m_mod_open =true;

    sendData("cfp2 read b4a0\r\n");

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开始打开调制开关.").arg(m_name), Logs::LEVEL_INFO);
}

void API_OpticalModule::controlModClose()
{
    m_mod_close =true;

    sendData("cfp2 read b050\r\n");

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开始关闭调制开关.").arg(m_name), Logs::LEVEL_INFO);
}

void API_OpticalModule::controlFreqAccurateOpen()
{
    m_freq_accurate_open = true;

    sendData("cfp2 read b050\r\n");

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开始打开频率精调开关.").arg(m_name), Logs::LEVEL_INFO);
}

void API_OpticalModule::controlFreqAccurateClose()
{
    m_freq_accurate_close = true;

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开始关闭频率精调开关.").arg(m_name), Logs::LEVEL_INFO);
}

void API_OpticalModule::controlLaserOn()
{
    sendData("cfp2 enable ignore laser\r\n");
}

void API_OpticalModule::controlStartScan(qint16 start_bin, qint16 end_bin, qint16 freq_vary)
{
    flag_sweep = 1;

    freq_vary_sign = (freq_vary >= 0) ? 1 : -1;

    quint32 cnt = 0;

//        qDebug() << QString("cfp2 set tx-fre-fine %1\r\n").arg(QString::number((quint16)start_bin, 10));
//    while (flag_sweep==1)
//    {
        sendData(QString("cfp2 set tx-fre-fine %1\r\n").arg(QString::number((quint16)start_bin, 10)));
//        delay_msec(700);
//        start_bin += freq_vary;
//        flag_sweep = (freq_vary_sign*(end_bin-start_bin))<=0?0:1;
//    }

}

void API_OpticalModule::controlLaserOff()
{
    sendData("cfp2 disable ignore laser\r\n");
}


void API_OpticalModule::handleTelnetData(const QString &message)
{

    if(!m_log_status)
    {
        QStringList listMessage = message.split("\r\n");
        QString control_result =  "";

        if(listMessage.size() >= 2)
        {
            control_result = listMessage.at(1).trimmed();
        }
        else {
            control_result = listMessage.at(0).trimmed();
        }

        if(control_result.startsWith("Username:"))
        {
            sendData(QString("%1\r\n").arg(m_username));

            m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开始登录telnet,用户名为[%2].").arg(m_name).arg(m_username), Logs::LEVEL_INFO);
        }
        if(control_result.startsWith("Password:"))
        {
            sendData(QString("%1\r\n").arg(m_password));

            m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开始登录telnet,密码为[%2].").arg(m_name).arg(m_password), Logs::LEVEL_INFO);
        }
        if(control_result.startsWith("DCI#"))
        {
            sendData("configure terminal\r\n");

            m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:telnet登录成功,登入终端.").arg(m_name), Logs::LEVEL_INFO);
        }
        if(control_result.startsWith("DCI(config)#"))
        {
            sendData("debug\r\n");

            m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:telnet登录成功,进入debug模式.").arg(m_name), Logs::LEVEL_INFO);
        }
        if(control_result.startsWith("DCI(debug)#"))
        {
            m_log_status = true;

            m_timer->start();
        }
    }
    else {
        QString regAddr = "", result = "", control_result = "";

        Type type = OTHER;

        handleMessage(message, control_result, regAddr, result, type);

        if(type == WRITE){

            signalControlResult(control_result);

            m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:接收到响应:{%2}.").arg(m_name).arg(control_result), Logs::LEVEL_INFO);
        }
        else if(type == READ){

            handleQueryMessage(regAddr, result);

            m_elapsed_timer.restart();
        }

        if(m_status_sent_open_prbs)
        {
            if(regAddr.toLower().trimmed() == "0xb011")
            {
                QTimer::singleShot(50, this, [=](){

                    sendData(QString("cfp2 write b011 0xb200\r\n"));

                    QTimer::singleShot(50, this, [=](){
                        sendData(QString("cfp2 write 9041 0xf\r\n"));


                        m_status_sent_open_prbs = false;
                    });
                });
            }

        }

        if(m_status_recv_open_prbs)
        {
            if(regAddr.toLower().trimmed() == "0xb012")
            {
                QTimer::singleShot(50, this, [=](){

                    sendData(QString("cfp2 write b012 0x7fff\r\n"));

                    QTimer::singleShot(50, this, [=](){
                        sendData(QString("cfp2 write 9042 0xf\r\n"));


                        m_status_recv_open_prbs = false;
                    });
                });
            }
        }

        if(m_status_close_all_prbs)
        {
            if(regAddr.toLower().trimmed() == "0xb011")
            {
                QTimer::singleShot(50, this, [=](){
                    quint16 uResult = result.toUShort(nullptr, 16);

                    sendData(QString("cfp2 write b011 0x3200\r\n"));

                    QTimer::singleShot(50, this, [=](){
                        sendData(QString("cfp2 write 9041 0\r\n"));


                        QTimer::singleShot(50, this, [=](){
                            sendData(QString("cfp2 read b012\r\n"));
                        });
                    });
                });
            }

            if(regAddr.toLower().trimmed() == "0xb012")
            {
                QTimer::singleShot(50, this, [=](){

                    sendData(QString("cfp2 write b012 0x32e6\r\n"));

                    QTimer::singleShot(50, this, [=](){
                        sendData(QString("cfp2 write 9042 0\r\n"));


                        m_status_close_all_prbs = false;

                        QTimer::singleShot(50, this, [=](){
                            m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:结束关闭prbs收/发计数.").arg(m_name), Logs::LEVEL_INFO);
                        });
                    });
                });
            }
        }

        if(m_set_output_power)
        {
            if(regAddr.toLower().trimmed() == "0xb4a0")
            {
                qint16 iResult = result.toUShort(nullptr, 16);

                if(qAbs(iResult * 0.01 - m_value_output_power) <= 1.5){
                    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:控制光模块的理想输出功率指令控制到位.").arg(m_name), Logs::LEVEL_INFO);

                    m_set_output_power = false;
                }
                else {
                    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:控制光模块的理想输出功率指令控制不到位(前:%2,后:%3).")
                                     .arg(m_name).arg(m_value_output_power).arg(iResult), Logs::LEVEL_WARN);
                }
            }
        }

        if(m_set_carrier_freq)
        {
            if(regAddr.toLower().trimmed() == "0xb450")
            {
                double f1 = m_cache_values.REG_SENT_CARRIER_FREQ_1.toUShort(nullptr, 16) * 1000;
                double f2 = m_cache_values.REG_SENT_CARRIER_FREQ_2.toUShort(nullptr, 16) * 0.05;
                double df = m_cache_values.REG_CARRIER_FREQ_OFFSET.toUShort(nullptr, 16) * 0.001;

                if(qAbs(f1 + f2 + df - m_value_carrier_freq) <= 1.5){
                    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:控制光模块的载波频率值指令控制到位.")
                                     .arg(m_name), Logs::LEVEL_INFO);
                }
                else {
                    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:控制光模块的载波频率值指令控制不到位(前:%2,后:%3).")
                                     .arg(m_name).arg(QString::number(f1 + f2 + df, 'f', 2)).arg(m_value_carrier_freq), Logs::LEVEL_WARN);
                }
            }
        }

        if(m_mod_open)
        {
            if(regAddr.toLower().trimmed() == "0xb4a0")
            {
                qint16 i16_result = result.toUShort(nullptr, 16);

                if(i16_result <= -500)
                {
                    sendData(QString("cfp2 write bc00 0x0101\r\n"));

                    sendData(QString("cfp2 write bc01 0x0701\r\n"));

                    sendData(QString("cfp2 write bc02 0x0000\r\n"));

                    sendData(QString("cfp2 write bc03 0x0007\r\n"));

                    sendData(QString("cfp2 write bc04 0x0206\r\n"));

                    sendData(QString("cfp2 write bc05 0x0000\r\n"));

                    sendData(QString("cfp2 write bc06 0x0a0f\r\n"));

                    QTimer::singleShot(50, this, [=](){
                        sendData(QString("cfp2 read b04e\r\n"));
                    });
                }
                else {
                    QTimer::singleShot(50, this, [=](){
                        sendData(QString("cfp2 read b4a0\r\n"));
                    });
                }
            }


            if(regAddr.toLower().trimmed() == "0xb04e")
            {
                quint16 i16_result = result.toUShort(nullptr, 16);

                sendData(QString("cfp2 write b04e 0x%1\r\n").arg(QString::number(i16_result | 0x8000, 16)));

                QTimer::singleShot(50, this, [=](){
                    sendData(QString("cfp2 write b042 2\r\n"));

                    m_mod_open = false;
                });
            }
        }



        if(m_mod_close)
        {
            if(regAddr.toLower().trimmed() == "0xb050")
            {
                quint16 i16_result = result.toUShort(nullptr, 16);

                if((i16_result & 0x8000) == 0x8000)
                {
                    sendData(QString("cfp2 write bc00 0x0101\r\n"));

                    sendData(QString("cfp2 write bc01 0x0701\r\n"));

                    sendData(QString("cfp2 write bc02 0x0000\r\n"));

                    sendData(QString("cfp2 write bc03 0x0007\r\n"));

                    sendData(QString("cfp2 write bc04 0x0206\r\n"));

                    sendData(QString("cfp2 write bc05 0x000f\r\n"));

                    sendData(QString("cfp2 write bc06 0x0a1e\r\n"));

                    QTimer::singleShot(50, this, [=](){
                        sendData(QString("cfp2 read b04e\r\n"));
                    });
                }
                else {
                    QTimer::singleShot(50, this, [=](){
                        sendData(QString("cfp2 read b050\r\n"));
                    });
                }
            }



            if(regAddr.toLower().trimmed() == "0xb04e")
            {
                quint16 i16_result = result.toUShort(nullptr, 16);

                sendData(QString("cfp2 write b04e 0x%1\r\n").arg(QString::number(i16_result | 0x8000, 16)));

                QTimer::singleShot(50, this, [=](){
                    sendData(QString("cfp2 write b042 2\r\n"));

                    m_mod_close = false;
                });
            }
        }





        //            {
        //                quint16 i16_result = result.toUShort(nullptr, 16);

        //                if((i16_result & 0x8000) == 0x8000)
        //                {
        //                    QTimer::singleShot(100, this, [=](){
        //                        sendData(QString("cfp2 write 0xbc00 0x0101\r\n"));
        //                        sendData(QString("cfp2 write 0xbc01 0x0603\r\n"));
        //                        sendData(QString("cfp2 write 0xbc02 0x0000\r\n"));
        //                        sendData(QString("cfp2 write 0xbc03 0x0006\r\n"));
        //                        sendData(QString("cfp2 write 0xbc04 0x0001\r\n"));
        //                        sendData(QString("cfp2 write 0xbc05 0x070b\r\n"));
        //                        sendData(QString("cfp2 read 0xb04e\r\n"));
        //                    });
        //                }
        //                else if ((i16_result & 0x0040) == 0x0040) {
        //                    QTimer::singleShot(100, this, [=](){
        //                        sendData(QString("cfp2 write 0xbc00 0x0101\r\n"));
        //                        sendData(QString("cfp2 write 0xbc01 0x0602\r\n"));
        //                        sendData(QString("cfp2 write 0xbc02 0x0000\r\n"));
        //                        sendData(QString("cfp2 write 0xbc03 0x0007\r\n"));
        //                        sendData(QString("cfp2 write 0xbc04 0x0062\r\n"));
        //                        sendData(QString("cfp2 write 0xbc05 0x070b\r\n"));
        //                        sendData(QString("cfp2 read 0xb04e\r\n"));
        //                    });
        //                }
        //                else {
        //                    sendData(QString("cfp2 read b050\r\n"));
        //                }
        //            }

        //            if(regAddr.toLower().trimmed() == "0xb04e")
        //            {
        //                quint16 i16_result = result.toUShort(nullptr, 16) | 0x8000;
        //                sendData(QString("cfp2 write 0xb04e 0x%1\r\n").arg(QString::number(i16_result, 16)));
        //                sendData(QString("cfp2 write 0xb042 2\r\n"));
        //            }
        //        }
    }
}

void API_OpticalModule::init()
{
    m_telnet = new QtTelnet(this);

    m_timer->setInterval(QUERY_TIME_INTERVAL_MS);

    connect(m_timer, &QTimer::timeout, this, &API_OpticalModule::queryData);

    initQueryMap();

    connect(m_telnet, &QtTelnet::message, this, &API_OpticalModule::handleTelnetData);

    m_params.insert("swep_num", 0);
}

void API_OpticalModule::handleQueryMessage(const QString &regAddr, const QString &result)
{
    quint16 u16RegAddr = regAddr.toUShort(nullptr, 16);

    switch (u16RegAddr) {

    case REG_STATUS:
        m_cache_values.REG_STATUS = result;
        break;

    case REG_OUTPUT_POWER:
        m_cache_values.REG_OUTPUT_POWER = result;
        break;

    case REG_INPUT_POWER:
        m_cache_values.REG_INPUT_POWER = result;
        break;

    case REG_SNR:
        m_cache_values.REG_SNR = result;
        break;

    case REG_SENT_CARRIER_FREQ_1:
        m_cache_values.REG_SENT_CARRIER_FREQ_1 = result;
        break;

    case REG_SENT_CARRIER_FREQ_2:
        m_cache_values.REG_SENT_CARRIER_FREQ_2 = result;
        break;

    case REG_CARRIER_FREQ_OFFSET:
        m_cache_values.REG_CARRIER_FREQ_OFFSET = result;
        break;

    case REG_CONTROL_CARRIER_FREQ:
        m_cache_values.REG_CONTROL_CARRIER_FREQ = result;
        break;

    case REG_CONTROL_DOPPLER_VALUE:
        m_cache_values.REG_CONTROL_DOPPLER_VALUE = result;
        break;

    case REG_CONTROL_OUTPUT_POWER:
        m_cache_values.REG_CONTROL_OUTPUT_POWER = result;
        break;

    case REG_TEMP:
        m_cache_values.REG_TEMP = result;
        break;

    case REG_EVM:
        m_cache_values.REG_EVM = result;
        break;

    case REG_MOD_STATUS:
        m_cache_values.REG_MOD_STATUS = result;
        break;

    case REG_BC04:
        m_cache_values.REG_BC04 = result;
        break;

    case REG_B050:
        m_cache_values.REG_B050 = result;
        break;

    case REG_ERROR_CODE_RATE_1:
        break;

    case REG_ERROR_CODE_RATE_2:
        break;

    case REG_BIT_COUNT_1:
        break;

    case REG_ERROR_BIT_COUNT_1:
        break;

    default:
        break;
    }
}

void API_OpticalModule::handleMessage(const QString &full_message, QString &part_message, QString &regAddr, QString &result, API_OpticalModule::Type &type)
{
    part_message = full_message.section("\r\n", 1, 1);


    if(full_message.contains("offset"))
    {
        if(part_message.contains(":"))
        {
            regAddr = part_message.section(':', 0, 0).section(' ', 2, 2);

            result = part_message.section(':', 1, 1).trimmed();
        }

        QString offset_value = full_message.section("\r\n", 3, 3).trimmed();

        QStringList list_offset_value = offset_value.split(" ");

        for(int i = 1; i < list_offset_value.size(); i++)
        {
            QString tmpRegAddr = regAddr;

            if(tmpRegAddr.isEmpty()) continue;

            tmpRegAddr.replace(tmpRegAddr.length() - 1, 1, tmpRegAddr[tmpRegAddr.length() - 1].toLatin1() + i - 1);

            QString result = list_offset_value.at(i);

            //            m_cache_values.insert(tmpRegAddr, &result);


            switch (tmpRegAddr.toUShort(nullptr, 16)) {
            case REG_BIT_COUNT_1:
                m_cache_values.REG_BIT_COUNT_1 = result;
                break;
            case REG_BIT_COUNT_2:
                m_cache_values.REG_BIT_COUNT_2 = result;
                break;
            case REG_BIT_COUNT_3:
                m_cache_values.REG_BIT_COUNT_3 = result;
                break;
            case REG_BIT_COUNT_4:
                m_cache_values.REG_BIT_COUNT_4 = result;
                break;
            case REG_ERROR_BIT_COUNT_1:
                m_cache_values.REG_ERROR_BIT_COUNT_1 = result;
                break;
            case REG_ERROR_BIT_COUNT_2:
                m_cache_values.REG_ERROR_BIT_COUNT_2 = result;
                break;
            case REG_ERROR_BIT_COUNT_3:
                m_cache_values.REG_ERROR_BIT_COUNT_3 = result;
                break;
            case REG_ERROR_BIT_COUNT_4:
                m_cache_values.REG_ERROR_BIT_COUNT_4 = result;
                break;
            case REG_ERROR_CODE_RATE_1:
                m_cache_values.REG_ERROR_CODE_RATE_1 = result;
                break;
            case REG_ERROR_CODE_RATE_2:
                m_cache_values.REG_ERROR_CODE_RATE_2 = result;
                break;
            case REG_PRBS_9041:
                m_cache_values.REG_PRBS_9041 = result;
                break;
            case REG_PRBS_9042:
                m_cache_values.REG_PRBS_9042 = result;
                break;
            }
        }

        type = READ_MULTI;
    }
    else if(full_message.contains("Read"))
    {
        if(part_message.contains(":"))
        {
            regAddr = part_message.section(':', 0, 0).section(' ', 2, 2);

            result = part_message.section(':', 1, 1).trimmed();
        }

        type = READ;
    }
    else if(full_message.contains("Write"))
    {
        if(part_message.contains(":"))
        {
            regAddr = part_message.section(':', 0, 0).section(' ', 2, 2);

            result = part_message.section(':', 1, 1).trimmed();
        }

        type = WRITE;
    }
}

void API_OpticalModule::delay_msec(qint32 msec)
{
    QTime time = QTime::currentTime().addMSecs(msec);

    while (QTime::currentTime() < time) {
        QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
    }
}

void API_OpticalModule::sendData(const QString &data)
{
    m_mutex.lock();

    m_telnet->sendData(data);

    //tsh
    //    qDebug() << data;

    m_mutex.unlock();
}

void API_OpticalModule::login(const QString &ip, const quint16 port)
{
    m_telnet->connectToHost(ip, port);

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:开始配置telnet信息,登录到[%2,%3].").arg(m_name).arg(ip).arg(port), Logs::LEVEL_INFO);

    m_ip = ip;

    m_port = port;
}

void API_OpticalModule::reConnect()
{
    return;

    m_telnet->logout();

    m_telnet->close();

    m_telnet->connectToHost(m_ip, m_port);

    m_telnet->login(m_username, m_password);

    m_log->writeLogs(LOGS_CODE_POS, QString("[%1]:telnet重新连接到[%2,%3].").arg(m_name).arg(m_ip).arg(m_port), Logs::LEVEL_INFO);
}


