#ifndef API_REDISCLIENT_H
#define API_REDISCLIENT_H

#include <QObject>
#include <adapters/qt.h>
#include <hiredis.h>
#include <QMutex>
#include <QMutexLocker>

class API_RedisClient : public QObject
{
    Q_OBJECT
public:
    static API_RedisClient* getInstance(){
        static QMutex mutex;

        if(!instance)
        {
            QMutexLocker locker(&mutex);
            if(!instance)
            {
                instance = new API_RedisClient;
            }
        }
        return instance;
    }

public:
    void login(const QString& ip, const quint16 port);

    void setValue(const QString& key, const QString& value);

    QString getValue(const QString& key);

private:
    API_RedisClient();

    ~API_RedisClient();

    static API_RedisClient* instance;

    redisContext* m_context = nullptr;

    redisReply* m_reply = nullptr;

    QString m_ip = "";

    quint16 m_port = 0;
};


#endif // API_REDISCLIENT_H
