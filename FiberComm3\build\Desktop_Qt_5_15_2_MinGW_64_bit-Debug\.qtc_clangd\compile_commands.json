[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm3\\api\\api_amplifier.cpp"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/api/api_amplifier.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm3\\api\\api_opticalmodule.cpp"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/api/api_opticalmodule.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm3\\api\\api_redisclient.cpp"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/api/api_redisclient.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm3\\api\\qttelnet.cpp"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/api/qttelnet.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm3\\main.cpp"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm3\\mainwindow.cpp"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm3\\tool\\logs.cpp"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/tool/logs.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm3\\tool\\netconfigsettings.cpp"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/tool/netconfigsettings.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm3\\ui\\formamplifier.cpp"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/ui/formamplifier.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm3\\ui\\formcombineddev.cpp"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/ui/formcombineddev.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\work\\code\\QtCode\\FiberComm3\\ui\\formopticalmodule.cpp"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/ui/formopticalmodule.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include\\adapters\\qt.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/QtHiRedis_Lib_PATH/include/adapters/qt.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\api\\api_redisclient.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/api/api_redisclient.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\api\\api_amplifier.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/api/api_amplifier.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\api\\api_opticalmodule.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/api/api_opticalmodule.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\api\\dev_amplifier.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/api/dev_amplifier.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\api\\dev_opticalmodule.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/api/dev_opticalmodule.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\api\\qttelnet.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/api/qttelnet.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\mainwindow.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\tool\\global.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/tool/global.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\tool\\logs.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/tool/logs.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\tool\\netconfigsettings.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/tool/netconfigsettings.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\ui\\formamplifier.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/ui/formamplifier.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\ui\\formcombineddev.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/ui/formcombineddev.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\ui\\formopticalmodule.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/ui/formopticalmodule.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\ui_formcombineddev.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/ui_formcombineddev.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\ui_mainwindow.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/ui_mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\ui_formopticalmodule.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/ui_formopticalmodule.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\soft\\QT\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3", "-IE:\\work\\code\\QtCode\\FiberComm3\\api", "-IE:\\work\\code\\QtCode\\FiberComm3\\ui", "-IE:\\work\\code\\QtCode\\FiberComm3\\tool", "-IE:\\work\\code\\QtCode\\FiberComm3\\QtHiRedis_Lib_PATH\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtWidgets", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtGui", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtANGLE", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtSerialPort", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtNetwork", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\include\\QtCore", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\debug", "-IE:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "-ID:\\soft\\QT\\Qt\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\soft\\QT\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\work\\code\\QtCode\\FiberComm3\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\ui_formamplifier.h"], "directory": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "E:/work/code/QtCode/FiberComm3/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/ui_formamplifier.h"}]