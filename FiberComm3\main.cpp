#include "mainwindow.h"
#include "formopticalmodule.h"
#include "formcombineddev.h"
#include <QByteArray>
#include <QFile>
#include <QApplication>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    QFile file(":/icon/image/ui.qss");

    if(!file.open(QIODevice::ReadOnly)) return 0;

    QString style_sheet = file.readAll();

    a.setStyleSheet(style_sheet);

    file.close();

    FormCombinedDev w(MACHINE_A);

   // w.showMaximized();
    w.show();
    return a.exec();
}
