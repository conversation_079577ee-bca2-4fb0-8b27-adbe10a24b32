#ifndef GLOBAL_H
#define GLOBAL_H

#include <QtGlobal>
#include <QString>
#include <QMessageBox>

namespace Common {

static const quint32 FIXED_HEIGHT = 24;
static const quint32 FIXED_WIDTH = 160;
static const QString& OFFLINE_VALUE = "--/--";
static char* MIN_VALUE = "min_value";
static char* MAX_VALUE = "max_value";
static const quint32 QUERY_EXPIRED_TIME = 10000;
static const quint32 CONTROL_EXPIRED_TIME = 1000;
static const double LIGHT_SPEED = 299792458;

typedef enum MACHINE_TYPE
{
    MACHINE_A,
    MACHINE_B
}MachineType;

static void showMessageBox(const QString& title, const QString& text)
{
    QMessageBox msgBox;
    msgBox.setIcon(QMessageBox::Information);
    msgBox.setText(text);
    msgBox.setWindowTitle(title);
    msgBox.setStyleSheet("color:#FFFFFF;");
    msgBox.exec();
}
}

#endif // GLOBAL_H
