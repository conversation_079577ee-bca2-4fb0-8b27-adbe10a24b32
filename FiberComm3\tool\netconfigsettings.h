#ifndef NETCONFIGSETTINGS_H
#define NETCONFIGSETTINGS_H

#include <QVariantMap>

class NetConfigSettings
{
private:
    static NetConfigSettings* instance;

public:
    NetConfigSettings();
    ~NetConfigSettings(){}

    NetConfigSettings(const NetConfigSettings& );
    NetConfigSettings& operator=(const NetConfigSettings&);

    QVariantMap getNetworkInfo(const QString& devid);

private:
    void readConfigInfo();

    void initConfigInfo();

    QMap<QString, QVariantMap> m_network_info;

private:
    class Deletor{
    public:
        ~Deletor(){
            if(NetConfigSettings::instance != nullptr)
                delete NetConfigSettings::instance;
        }
    };

    static Deletor deletor;

public:
    static NetConfigSettings* getInstance()
    {
        if(instance == nullptr)
        {
            instance = new NetConfigSettings();
        }
        return instance;
    }
};

#endif // NETCONFIGSETTINGS_H
