<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FormAmplifier</class>
 <widget class="QWidget" name="FormAmplifier">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1121</width>
    <height>894</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="font">
      <font>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="title">
      <string>模块状态</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1,0,1,1,0,1,1,0,1,1,0">
        <item>
         <widget class="QLabel" name="label_11">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>温度高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="module_warn_high_temp">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_12">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>温度低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="module_warn_low_temp">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_7">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_14">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输入电压：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="module_warn_input_power">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_8">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_13">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输出电压：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="module_warn_output_power">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_19">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_15" stretch="1,1,0,1,1,0,1,1">
        <item>
         <widget class="QLabel" name="label">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>温度(℃)：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="module_temp">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>36</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_34">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_2">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>模块输入电压(mV)：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="module_input_vol">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>36</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_35">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_58">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>上电状态：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="online_status">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>36</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_2">
     <property name="font">
      <font>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="title">
      <string>低噪声状态</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,1,0,1,1,0,1,1,0,1,1,0">
        <item>
         <widget class="QLabel" name="label_15">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输入功率高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="noise_warn_input_high_power">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_10">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_16">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输入功率低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="noise_warn_input_low_power">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_11">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_18">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输出功率高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="noise_warn_output_high_power">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_13">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_17">
          <property name="font">
           <font>
            <pointsize>16</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输出功率低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="noise_warn_output_low_power">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_9">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout" columnstretch="1,1,0,1,1,0,1,1,0,1,1">
        <item row="0" column="10">
         <widget class="QLabel" name="noise_output_power">
          <property name="font">
           <font>
            <pointsize>16</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="8">
         <spacer name="horizontalSpacer_38">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="3">
         <widget class="QLabel" name="label_5">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>开关：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_3">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>模式：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLabel" name="noise_mode">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>36</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="7">
         <widget class="QLabel" name="noise_input_power">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="9">
         <widget class="QLabel" name="label_8">
          <property name="font">
           <font>
            <pointsize>16</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输出功率(dBm)：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="5">
         <spacer name="horizontalSpacer_37">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="2">
         <spacer name="horizontalSpacer_36">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="4">
         <widget class="QLabel" name="noise_switch">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="6">
         <widget class="QLabel" name="label_7">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输入功率(dBm)：</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_3">
     <property name="font">
      <font>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="title">
      <string>高功率状态</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,1,0,1,1,0,1,1,0,1,1,0">
        <item>
         <widget class="QLabel" name="label_24">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输入功率高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="power_warn_input_high_power">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_14">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_26">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输入功率低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="power_warn_input_low_power">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_16">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_22">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输出功率高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="power_warn_output_high_power">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_17">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_21">
          <property name="font">
           <font>
            <pointsize>16</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输出功率低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="power_warn_output_low_power">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_15">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout_2" columnstretch="1,1,0,1,1,0,1,1,0,1,1">
        <item row="0" column="2">
         <spacer name="horizontalSpacer_39">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="7">
         <widget class="QLabel" name="power_input_power">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLabel" name="power_mode">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>36</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="4">
         <widget class="QLabel" name="power_switch">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="9">
         <widget class="QLabel" name="label_19">
          <property name="font">
           <font>
            <pointsize>16</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输出功率(dBm)：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="10">
         <widget class="QLabel" name="power_output_power">
          <property name="font">
           <font>
            <pointsize>16</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QLabel" name="label_25">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>开关：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="6">
         <widget class="QLabel" name="label_20">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>输入功率(dBm)：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_23">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>模式：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="5">
         <spacer name="horizontalSpacer_40">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="8">
         <spacer name="horizontalSpacer_41">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_4">
     <property name="font">
      <font>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="title">
      <string>泵浦1状态</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_5">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_14" stretch="1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0">
        <item>
         <widget class="QLabel" name="label_34">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>电流高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_current_high_1">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_20">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_32">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>电流低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_current_low_1">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_21">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_31">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>温度高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_temp_high_1">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_22">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_33">
          <property name="text">
           <string>温度低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_temp_low_1">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_23">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_37">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>背光高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_backlight_high_1">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_24">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_38">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>背光低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_backlight_low_1">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_25">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_36">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>TEC电流高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_tec_current_high_1">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_26">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_35">
          <property name="text">
           <string>TEC电流低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_tec_current_low_1">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="1,1,0,1,1,0,1,1,0,1,1">
        <item>
         <widget class="QLabel" name="label_27">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>电流(mA)：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="current_1">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>36</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_42">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_28">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>温度(℃)：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="temp_1">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_43">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_29">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>背光电流(mA)：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="backlight_current_1">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_44">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_30">
          <property name="text">
           <string>TEC电流(mA)：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="tec_current_1">
          <property name="font">
           <font>
            <pointsize>16</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_5">
     <property name="font">
      <font>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="title">
      <string>泵浦2状态</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_6">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_16" stretch="1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0">
        <item>
         <widget class="QLabel" name="label_39">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>电流高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_current_high_2">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_27">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_40">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>电流低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_current_low_2">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_28">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_41">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>温度高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_temp_high_2">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_29">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_42">
          <property name="text">
           <string>温度低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_temp_low_2">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_30">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_43">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>背光高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_backlight_high_2">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_31">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_44">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>背光低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_backlight_low_2">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_32">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_45">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>TEC电流高：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_tec_current_high_2">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_33">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_46">
          <property name="text">
           <string>TEC电流低：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="warn_tec_current_low_2">
          <property name="minimumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="1,1,0,1,1,0,1,1,0,1,1">
        <item>
         <widget class="QLabel" name="label_47">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>电流(mA)：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="current_2">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>36</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_45">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_48">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>温度(℃)：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="temp_2">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_46">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_49">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>背光电流(mA)：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="backlight_current_2">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_47">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_50">
          <property name="text">
           <string>TEC电流(mA)：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="tec_current_2">
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>TextLabel</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QGridLayout" name="gridLayout_12">
     <item row="0" column="0">
      <widget class="QGroupBox" name="groupBox_6">
       <property name="font">
        <font>
         <pointsize>16</pointsize>
        </font>
       </property>
       <property name="title">
        <string>设置通道开关状态</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_6">
        <item>
         <layout class="QGridLayout" name="gridLayout_6" columnstretch="1,1,1,1,0">
          <item row="0" column="1">
           <widget class="QComboBox" name="channel_id_A001">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="label_4">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>通道号：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="label_6">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>开关状态：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QComboBox" name="switch_status_A001">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QPushButton" name="btnA001">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>设置</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QGroupBox" name="groupBox_7">
       <property name="font">
        <font>
         <pointsize>16</pointsize>
        </font>
       </property>
       <property name="title">
        <string>设置通道工作模式</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_7">
        <item>
         <layout class="QGridLayout" name="gridLayout_7" columnstretch="1,1,1,1,0">
          <item row="0" column="2">
           <widget class="QLabel" name="label_10">
            <property name="text">
             <string>工作模式：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QComboBox" name="channel_id_A002">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="label_9">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>通道号：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QComboBox" name="work_mode_A002">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QPushButton" name="btnA002">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>设置</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QGroupBox" name="groupBox_8">
       <property name="font">
        <font>
         <pointsize>16</pointsize>
        </font>
       </property>
       <property name="title">
        <string>设置泵浦电流值</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_9">
        <item>
         <layout class="QGridLayout" name="gridLayout_9" columnstretch="1,1,1,1,0">
          <item row="0" column="3">
           <widget class="QLineEdit" name="current_A003">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QComboBox" name="pump_id_A003">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="label_53">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>泵浦编号：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="label_54">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>电流值(mA)：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QPushButton" name="btnA003">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>设置</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QGroupBox" name="groupBox_9">
       <property name="font">
        <font>
         <pointsize>16</pointsize>
        </font>
       </property>
       <property name="title">
        <string>设置通道输出功率值</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_10">
        <item>
         <layout class="QGridLayout" name="gridLayout_10" columnstretch="1,1,1,1,0">
          <item row="0" column="1">
           <widget class="QComboBox" name="channel_id_A004">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="label_55">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>通道号：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QLineEdit" name="output_power_A004">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(0, 85, 0);</string>
            </property>
            <property name="text">
             <string>-60</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="label_56">
            <property name="text">
             <string>功率值(dBm)：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QPushButton" name="btnA004">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>设置</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QGroupBox" name="groupBox_10">
       <property name="font">
        <font>
         <pointsize>16</pointsize>
        </font>
       </property>
       <property name="title">
        <string>设置上电状态</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_11">
        <item>
         <layout class="QGridLayout" name="gridLayout_11" columnstretch="1,1,1">
          <item row="0" column="0">
           <widget class="QLabel" name="label_57">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>上电状态：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QComboBox" name="online_status_A007">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QPushButton" name="btnA007">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>36</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>14</pointsize>
             </font>
            </property>
            <property name="text">
             <string>设置</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
