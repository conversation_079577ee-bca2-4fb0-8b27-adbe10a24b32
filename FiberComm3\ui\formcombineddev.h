#ifndef FORMCOMBINEDDEV_H
#define FORMCOMBINEDDEV_H

#include <QWidget>
#include <QTimerEvent>
#include <QLabel>
#include <QCloseEvent>
#include <QDateTime>

#include "api_opticalmodule.h"
#include "api_amplifier.h"
#include "formamplifier.h"
#include "global.h"

using namespace Common;

namespace Ui {
class FormCombinedDev;
}

class FormCombinedDev : public QWidget
{
    Q_OBJECT

public:
    typedef enum LIGHT_COLOR{
        GRAY, GREEN, RED, YELLOW
    }LightColor;

    explicit FormCombinedDev(MachineType type, QWidget *parent = nullptr);
    ~FormCombinedDev();


protected:

    void closeEvent(QCloseEvent* event) override;

float convertAndRoundScientific(const QString& sciStr);
    float convertHexToClampedFloat(const QString& hexStr);

private slots:

    void refData();

    void on_btnSet_1_clicked();

    void on_btnSet_2_clicked();

    void on_btnSet_3_clicked();

    void on_btnSet_4_clicked();

    void on_btnSet_6_clicked();

    void on_btnSet_7_clicked();

    void on_btnSet_8_clicked();

    void on_btnSet_10_clicked();

    void on_btnSet_11_clicked();

    void on_btnSet_12_clicked();

    void on_btnSet_14_clicked();

    void on_btnSet_15_clicked();

    void on_btnAmplifier_clicked();

    void on_btnRecordData_clicked();

    void on_btnOpenData_clicked();

private:
    int find_ber_target(float target);
    void readtextfile();
    void initView();

    void initMember();

    void initControls();

    void initComboxs();

    void initTooltips();

    void initDefaultParams();

    void loadLastParams();

    void setLightColor(QLabel* label, const LightColor color);

    double waveLen2freqSet(API_OpticalModule* api_optical_module, double wave_len);

    double freqSet2waveLen(API_OpticalModule* api_optical_module, double freq_set);

    int findClosestIndex(const QVector<double>& array, double target);

private slots:

    void on_ctrl_channel_num_1_currentIndexChanged(int index);

    void on_ctrl_channel_num_2_currentIndexChanged(int index);

    void on_btnSet_13_clicked();

    void on_btnSet_18_clicked();

    void on_btnSet_16_clicked();

    void on_btnSet_17_clicked();

    void on_btnSet_19_clicked();

    void on_btnSet_20_clicked();

    void on_btnSet_21_clicked();

    void on_btnSet_22_clicked();

    void on_btnSet_25_clicked();

    void on_btnSet_23_clicked();

    void on_btnSet_24_clicked();

    void on_ctrl_module_status_1_activated(const QString &arg1);

    void on_btnSet_9_clicked();

    void on_btnSet_27_clicked();

    void on_btnSet_26_clicked();

    void on_btnSet_28_clicked();

    void on_sta_scanstart_linkActivated(const QString &link);

private:
    Ui::FormCombinedDev *ui;

    MachineType m_machine_type = MACHINE_A;

    API_OpticalModule* m_api_optical_module_1 = nullptr;
    API_OpticalModule* m_api_optical_module_2 = nullptr;
    API_Amplifier* m_api_amplifier = nullptr;
    FormAmplifier* m_form_amplifier = nullptr;

    QTimer* m_timer_module_status_1 = nullptr;
    QTimer* m_timer_module_status_2 = nullptr;

    bool m_record_status = false;
    QFile* m_record_file = nullptr;

    QTimer* m_timer_reset_ready_time_1 = nullptr;
    QDateTime m_start_time_1;

    QTimer* m_timer_reset_ready_time_2 = nullptr;
    QDateTime m_start_time_2;

    QVector<double> table_list;
    QList<QString>list_ber1;
    QList<QString>list_ber2;

};

#endif // FORMCOMBINEDDEV_H
