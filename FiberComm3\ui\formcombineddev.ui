<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FormCombinedDev</class>
 <widget class="QWidget" name="FormCombinedDev">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1562</width>
    <height>987</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_7">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_24" stretch="1,1">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_5">
       <item>
        <widget class="QGroupBox" name="groupBox">
         <property name="font">
          <font>
           <pointsize>14</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QGroupBox#groupBox::title{
	font-weight:bold; 
	font-size:30px; 
}</string>
         </property>
         <property name="title">
          <string notr="true">发送端</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout" stretch="1,6">
          <property name="topMargin">
           <number>19</number>
          </property>
          <item>
           <widget class="QGroupBox" name="groupBox_14">
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>70</height>
             </size>
            </property>
            <property name="title">
             <string/>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_11">
             <item>
              <layout class="QGridLayout" name="gridLayout">
               <item row="0" column="8">
                <spacer name="horizontalSpacer_6">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item row="0" column="10">
                <widget class="QLabel" name="sta_amplifier_status_1">
                 <property name="minimumSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                   <weight>50</weight>
                   <bold>false</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                 <property name="scaledContents">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="0" column="7">
                <widget class="QLabel" name="sta_trans_1">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                   <weight>50</weight>
                   <bold>false</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                 <property name="scaledContents">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="0" column="2">
                <spacer name="horizontalSpacer">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item row="0" column="6">
                <widget class="QLabel" name="label_5">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                   <weight>50</weight>
                   <bold>false</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>通信：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="5">
                <spacer name="horizontalSpacer_2">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item row="0" column="1">
                <widget class="QLabel" name="sta_reset_1">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                   <weight>50</weight>
                   <bold>false</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                 <property name="scaledContents">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="0" column="3">
                <widget class="QLabel" name="label_2">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                   <weight>50</weight>
                   <bold>false</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>就绪：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="9">
                <widget class="QLabel" name="label_21">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                   <weight>50</weight>
                   <bold>false</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>功放：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="0">
                <widget class="QLabel" name="label_19">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                   <weight>50</weight>
                   <bold>false</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>复位：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="4">
                <widget class="QLabel" name="sta_ready_1">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                   <weight>50</weight>
                   <bold>false</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                 <property name="scaledContents">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <layout class="QGridLayout" name="gridLayout_2">
            <item row="0" column="2">
             <widget class="QLabel" name="label_3">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>光模块输出功率(dBm)：</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLabel" name="module_temp_1">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_28">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>高功放输入(dBm)：</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_10">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>光模块温度(℃)：</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLabel" name="hpa_input_power_1">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="4" column="1">
             <widget class="QLabel" name="carrier_power_1">
              <property name="minimumSize">
               <size>
                <width>180</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QLabel" name="label_16">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>发射光频率(GHz)：</string>
              </property>
             </widget>
            </item>
            <item row="0" column="3">
             <widget class="QLabel" name="output_power_1">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="2" column="2">
             <widget class="QLabel" name="label_4">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>高功放输出(dBm)：</string>
              </property>
             </widget>
            </item>
            <item row="2" column="3">
             <widget class="QLabel" name="hpa_output_power_1">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="4" column="2">
             <widget class="QLabel" name="label_41">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>发射光波长：</string>
              </property>
             </widget>
            </item>
            <item row="4" column="3">
             <widget class="QLabel" name="wave_len_1">
              <property name="minimumSize">
               <size>
                <width>180</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <item>
          <widget class="QGroupBox" name="groupBox_3">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_12">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1,1">
              <item>
               <widget class="QLabel" name="label_11">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>模式选择：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="ctrl_module_status_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_20">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_33" stretch="0,0,1,1">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <widget class="QLabel" name="label_38">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>激光基带调制：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="sta_mod_status_1">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="scaledContents">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="btnSet_11">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>调制打开</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="btnSet_19">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>调制关闭</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_4">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_13">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,1,1">
              <item>
               <widget class="QLabel" name="label_12">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>光模块输出功率(dBm)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="ctrl_output_power_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>0</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_16">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_25">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_26" stretch="0,1,1,1">
              <item>
               <widget class="QLabel" name="label_34">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>高功放工作模式：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="sta_hpa_workmode_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="ctrl_hpa_workmode_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_13">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_5">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_16">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,1,1">
              <item>
               <widget class="QLabel" name="label_13">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>高功放输出功率(dBm)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="ctrl_hpa_output_power_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>12</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_3">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_18">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_27">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_28" stretch="1,1,1,1">
              <item>
               <widget class="QLabel" name="label_35">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>高功放泵浦电流(mA)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="sta_current_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="ctrl_current_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>100</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_16">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_6">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_17">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="0,1,0,1">
              <item>
               <widget class="QLabel" name="label_14">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>激光波段(C13-C61)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="wave_length_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="ctrl_channel_num_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_4">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_7">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_18">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="1,1,1">
              <item>
               <widget class="QLabel" name="label_23">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>固定偏差(MHz)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="ctrl_freq_compensate_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>0</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_23">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_21">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_34">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_35" stretch="1,1,1">
              <item>
               <widget class="QLabel" name="label_39">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>发射波长(nm)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="ctrl_wave_compensate_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>0</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_20">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_23">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_38">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_39" stretch="1,1,1">
              <item>
               <widget class="QLabel" name="label_43">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>发射频率(GHz)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="ctrl_freq_1">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>0</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_22">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_25">
           <property name="title">
            <string/>
           </property>
           <layout class="QGridLayout" name="gridLayout_5" columnstretch="1,1,1,1,1,1,0,0,0,0,0,0">
            <item row="0" column="5">
             <widget class="QLineEdit" name="ctrl_start_bin">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>0</string>
              </property>
             </widget>
            </item>
            <item row="0" column="9">
             <widget class="QPushButton" name="btnSet_26">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>频率精调</string>
              </property>
             </widget>
            </item>
            <item row="0" column="6">
             <widget class="QLabel" name="sta_raser_on">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="scaledContents">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="0" column="7">
             <widget class="QPushButton" name="btnSet_27">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>激光器忽略关闭</string>
              </property>
             </widget>
            </item>
            <item row="0" column="8">
             <widget class="QLabel" name="sta_scanstart">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="scaledContents">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="0" column="10">
             <widget class="QLabel" name="sta_raser_off">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="scaledContents">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="0" column="11">
             <widget class="QPushButton" name="btnSet_28">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>激光器启用关闭</string>
              </property>
             </widget>
            </item>
            <item row="0" column="4">
             <widget class="QLabel" name="label_47">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>频偏(MHz)：</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_12">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_14" stretch="0,1,1">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <widget class="QLabel" name="sta_open_prbs">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="scaledContents">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="btnSet_10">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>打开线路侧PRBS发数</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="btnSet_12">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>关闭线路侧PRBS发数</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_6" stretch="6,8">
       <item>
        <widget class="QGroupBox" name="groupBox_2">
         <property name="font">
          <font>
           <pointsize>14</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QGroupBox#groupBox_2::title{
	font-weight:bold; 
	font-size:30px; 
}</string>
         </property>
         <property name="title">
          <string>接收端</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_2" stretch="1,11">
          <property name="topMargin">
           <number>40</number>
          </property>
          <item>
           <widget class="QGroupBox" name="groupBox_15">
            <property name="title">
             <string/>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_23">
             <item>
              <layout class="QGridLayout" name="gridLayout_3">
               <item row="0" column="9">
                <widget class="QLabel" name="label_22">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>功放：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="4">
                <widget class="QLabel" name="sta_ready_2">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                 <property name="scaledContents">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="0" column="2">
                <spacer name="horizontalSpacer_3">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item row="0" column="0">
                <widget class="QLabel" name="label_20">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>复位：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="8">
                <spacer name="horizontalSpacer_4">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item row="0" column="3">
                <widget class="QLabel" name="label_6">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>就绪：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="5">
                <spacer name="horizontalSpacer_7">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item row="0" column="1">
                <widget class="QLabel" name="sta_reset_2">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                 <property name="scaledContents">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="0" column="7">
                <widget class="QLabel" name="sta_trans_2">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                 <property name="scaledContents">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="0" column="6">
                <widget class="QLabel" name="label_7">
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>通信：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="10">
                <widget class="QLabel" name="sta_amplifier_status_2">
                 <property name="minimumSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                 <property name="scaledContents">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <layout class="QGridLayout" name="gridLayout_4">
            <item row="6" column="0">
             <widget class="QLabel" name="label_18">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>CFO估计值(MHz)：</string>
              </property>
             </widget>
            </item>
            <item row="6" column="1">
             <widget class="QLabel" name="freq_offset_2">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLabel" name="hpa_input_power_2">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QLabel" name="label_17">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>本振光频率(GHz)：</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_15">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>光模块温度(℃)：</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLabel" name="module_temp_2">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="4" column="1">
             <widget class="QLabel" name="carrier_power_2">
              <property name="minimumSize">
               <size>
                <width>180</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QLabel" name="label_8">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>光模块输入功率(dBm)：</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_31">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>低噪放输入(dBm)：</string>
              </property>
             </widget>
            </item>
            <item row="0" column="3">
             <widget class="QLabel" name="output_power_2">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="2" column="2">
             <widget class="QLabel" name="label_9">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>低噪放输出(dBm)：</string>
              </property>
             </widget>
            </item>
            <item row="2" column="3">
             <widget class="QLabel" name="hpa_output_power_2">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="4" column="2">
             <widget class="QLabel" name="label_42">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>本振光波长：</string>
              </property>
             </widget>
            </item>
            <item row="4" column="3">
             <widget class="QLabel" name="wave_len_2">
              <property name="minimumSize">
               <size>
                <width>180</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="6" column="2">
             <widget class="QLabel" name="label_24">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>接收光信噪比(dB)：</string>
              </property>
             </widget>
            </item>
            <item row="6" column="3">
             <widget class="QLabel" name="snr_2">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="10" column="0">
             <widget class="QLabel" name="label_32">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>EVM(%)：</string>
              </property>
             </widget>
            </item>
            <item row="10" column="1">
             <widget class="QLabel" name="evm_2">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="7" column="2">
             <widget class="QLabel" name="label_25">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>纠后误码率：</string>
              </property>
             </widget>
            </item>
            <item row="7" column="0">
             <widget class="QLabel" name="label_33">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>纠前误码率：</string>
              </property>
             </widget>
            </item>
            <item row="7" column="1">
             <widget class="QLabel" name="error_code_rate_2">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
            <item row="7" column="3">
             <widget class="QLabel" name="bit_count_2">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>TextLabel</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <item>
          <widget class="QGroupBox" name="groupBox_8">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_19">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="1,1,1">
              <item>
               <widget class="QLabel" name="label_26">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>模式选择：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="ctrl_module_status_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_6">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_17">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_30">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_31" stretch="0,1,1,1">
              <item>
               <widget class="QLabel" name="label_37">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>低噪放工作模式：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="sta_hpa_workmode_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="ctrl_hpa_workmode_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_18">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_9">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_20">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="1,1,1">
              <item>
               <widget class="QLabel" name="label_27">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>低噪放输出功率(dBm)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="ctrl_hpa_output_power_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>-5</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_7">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_19">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_29">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_32" stretch="1,1,1,1">
              <item>
               <widget class="QLabel" name="label_36">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>低噪放泵浦电流(mA)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="sta_current_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="ctrl_current_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>80</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_17">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_10">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_21">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="0,1,1,1">
              <item>
               <widget class="QLabel" name="label_29">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>激光波段(C13-C61)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="wave_length_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="ctrl_channel_num_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_8">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_11">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_22">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="0,1,1">
              <item>
               <widget class="QLabel" name="label_30">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>固定偏差(MHz)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="ctrl_freq_compensate_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>0</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_9">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_22">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_36">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_37" stretch="1,1,1">
              <item>
               <widget class="QLabel" name="label_40">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>接收波长(nm)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="ctrl_wave_compensate_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>0</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_21">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_24">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_44">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_45" stretch="1,1,1">
              <item>
               <widget class="QLabel" name="label_46">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>接收频率(GHz)：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="ctrl_freq_2">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>0</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnSet_25">
                <property name="font">
                 <font>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>设置</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_13">
           <property name="title">
            <string/>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_15" stretch="1,2,2">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>6</number>
            </property>
            <property name="rightMargin">
             <number>6</number>
            </property>
            <property name="bottomMargin">
             <number>9</number>
            </property>
            <item>
             <widget class="QLabel" name="sta_recv_prbs">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="scaledContents">
               <bool>true</bool>
              </property>
              <property name="alignment">
               <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="btnSet_14">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>打开线路侧PRBS收数</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="btnSet_15">
              <property name="font">
               <font>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="text">
               <string>关闭线路侧PRBS收数</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_10">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <spacer name="horizontalSpacer_5">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="btnAmplifier">
       <property name="font">
        <font>
         <pointsize>14</pointsize>
        </font>
       </property>
       <property name="text">
        <string>功放详情</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btnRecordData">
       <property name="font">
        <font>
         <pointsize>14</pointsize>
        </font>
       </property>
       <property name="text">
        <string>开始记录</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btnOpenData">
       <property name="font">
        <font>
         <pointsize>14</pointsize>
        </font>
       </property>
       <property name="text">
        <string>打开记录表</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
